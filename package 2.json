{"name": "screen-recorder", "version": "1.0.0", "description": "A Screen Studio alternative for screen recording", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "npm run build:renderer && electron-builder", "build:renderer": "react-scripts build", "start:renderer": "react-scripts start"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "react-scripts": "5.0.1"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "fluent-ffmpeg": "^2.1.2"}, "homepage": "./", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}