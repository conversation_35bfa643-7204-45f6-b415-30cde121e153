import React, { useState, useRef, useEffect } from 'react';
import './index.css';

function App() {
  const [isRecording, setIsRecording] = useState(false);
  const [sources, setSources] = useState([]);
  const [selectedSource, setSelectedSource] = useState(null);
  const [recordedBlob, setRecordedBlob] = useState(null);
  const [permissions, setPermissions] = useState({});
  const [status, setStatus] = useState('Ready to record');
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [showExportOptions, setShowExportOptions] = useState(false);
  
  const videoRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const streamRef = useRef(null);
  const durationIntervalRef = useRef(null);
  
  useEffect(() => {
    checkPermissions();
    loadSources();
  }, []);
  
  const checkPermissions = async () => {
    const perms = await window.electronAPI.checkPermissions();
    setPermissions(perms);
  };
  
  const requestPermissions = async () => {
    const granted = await window.electronAPI.requestPermissions();
    if (granted) {
      await checkPermissions();
    }
  };
  
  const loadSources = async () => {
    const sources = await window.electronAPI.getSources();
    setSources(sources);
    if (sources.length > 0 && !selectedSource) {
      // Auto-select the first screen
      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));
      setSelectedSource(screen || sources[0]);
    }
  };
  
  const startRecording = async () => {
    if (!selectedSource) {
      setStatus('Please select a screen or window to record');
      return;
    }
    
    try {
      setStatus('Starting recording...');
      setRecordingDuration(0);
      
      // Start duration timer
      durationIntervalRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
      
      // Get screen stream
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: false,
        video: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: selectedSource.id,
            minWidth: 1280,
            maxWidth: 1920,
            minHeight: 720,
            maxHeight: 1080,
            minFrameRate: 30
          }
        }
      });
      
      // Get microphone stream
      let audioStream = null;
      try {
        audioStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          },
          video: false
        });
      } catch (audioError) {
        console.warn('Microphone access denied, recording video only');
      }
      
      // Combine streams
      let combinedStream = stream;
      if (audioStream) {
        const audioTracks = audioStream.getAudioTracks();
        audioTracks.forEach(track => combinedStream.addTrack(track));
      }
      
      streamRef.current = combinedStream;
      
      // Set up MediaRecorder
      const mediaRecorder = new MediaRecorder(combinedStream, {
        mimeType: 'video/webm;codecs=vp9,opus'
      });
      
      const chunks = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'video/webm' });
        setRecordedBlob(blob);
        setStatus('Recording saved. You can now download it.');
        
        // Show preview
        if (videoRef.current) {
          videoRef.current.src = URL.createObjectURL(blob);
        }
      };
      
      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start(100); // Collect data every 100ms
      
      setIsRecording(true);
      setStatus('Recording in progress');
      
    } catch (error) {
      console.error('Failed to start recording:', error);
      setStatus('Failed to start recording. Please check permissions.');
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      
      // Stop duration timer
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
      
      // Stop all tracks
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      setIsRecording(false);
      setStatus('Processing recording...');
    }
  };
  
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const downloadRecording = async (format = 'webm') => {
    if (recordedBlob) {
      try {
        if (format === 'webm') {
          // Direct WebM download
          const url = URL.createObjectURL(recordedBlob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
          a.click();
          URL.revokeObjectURL(url);
          setShowExportOptions(false);
        } else if (format === 'mp4') {
          setStatus('Converting to MP4...');
          
          // Convert blob to buffer
          const arrayBuffer = await recordedBlob.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          
          // Save and convert via Electron
          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
          const savedPath = await window.electronAPI.saveRecording(buffer, filename);
          
          if (savedPath) {
            setStatus('Recording saved as MP4!');
            setShowExportOptions(false);
          }
        }
      } catch (error) {
        console.error('Failed to save recording:', error);
        setStatus('Failed to save recording');
      }
    }
  };
  
  const clearRecording = () => {
    setRecordedBlob(null);
    if (videoRef.current) {
      videoRef.current.src = '';
    }
    setStatus('Ready to record');
  };
  
  return (
    <div className="app">
      <header className="header">
        <h1>Screen Recorder</h1>
        <div className="status-info">
          {permissions.screen === 'denied' && (
            <button className="btn btn-secondary" onClick={requestPermissions}>
              Grant Permissions
            </button>
          )}
        </div>
      </header>
      
      <main className="content">
        <div className="source-selector">
          <h3>Select Screen or Window:</h3>
          <div className="sources-grid">
            {sources.map((source) => (
              <div
                key={source.id}
                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}
                onClick={() => setSelectedSource(source)}
              >
                <img
                  src={source.thumbnail}
                  alt={source.name}
                  className="source-thumbnail"
                />
                <div className="source-name">{source.name}</div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="recording-area">
          {recordedBlob ? (
            <div>
              <video
                ref={videoRef}
                className="preview-video"
                controls
                autoPlay={false}
              />
              <div className="recording-info">
                <p>Recording completed • Duration: {formatDuration(recordingDuration)}</p>
              </div>
            </div>
          ) : (
            <div className="status-display">
              <div className={`status-box ${isRecording ? 'recording' : ''}`}>
                {isRecording ? (
                  <div className="recording-status">
                    <div className="recording-indicator">
                      <span className="recording-dot"></span>
                      RECORDING
                    </div>
                    <div className="recording-duration">
                      {formatDuration(recordingDuration)}
                    </div>
                    <div className="recording-source">
                      {selectedSource?.name}
                    </div>
                  </div>
                ) : (
                  <div className="ready-status">
                    <div className="status-text">{status}</div>
                    {selectedSource && (
                      <div className="selected-source">Ready to record: {selectedSource.name}</div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        
        <div className="controls">
          {!isRecording && !recordedBlob && (
            <button
              className="btn btn-primary"
              onClick={startRecording}
              disabled={!selectedSource || permissions.screen === 'denied'}
            >
              Start Recording
            </button>
          )}
          
          {isRecording && (
            <button className="btn btn-danger" onClick={stopRecording}>
              Stop Recording
            </button>
          )}
          
          {recordedBlob && !isRecording && (
            <>
              {!showExportOptions ? (
                <button 
                  className="btn btn-primary" 
                  onClick={() => setShowExportOptions(true)}
                >
                  Download Recording
                </button>
              ) : (
                <div className="export-options">
                  <h4>Choose format:</h4>
                  <button 
                    className="btn btn-primary" 
                    onClick={() => downloadRecording('webm')}
                  >
                    Download WebM (Fast)
                  </button>
                  <button 
                    className="btn btn-primary" 
                    onClick={() => downloadRecording('mp4')}
                  >
                    Download MP4 (Compatible)
                  </button>
                  <button 
                    className="btn btn-secondary" 
                    onClick={() => setShowExportOptions(false)}
                  >
                    Cancel
                  </button>
                </div>
              )}
              <button className="btn btn-secondary" onClick={clearRecording}>
                New Recording
              </button>
            </>
          )}
          
          <button className="btn btn-secondary" onClick={loadSources}>
            Refresh Sources
          </button>
          
          <button 
            className="btn" 
            style={{background: 'purple', color: 'white'}} 
            onClick={() => alert('TEST: Code changes are working!')}
          >
            TEST BUTTON
          </button>
        </div>
      </main>
    </div>
  );
}

export default App;
