/// <reference path="./electron.d.ts" />
import { useState, useRef, useEffect } from 'react';
import './index.css';

function App() {
  const [isRecording, setIsRecording] = useState(false);
  const [sources, setSources] = useState([]);
  const [selectedSource, setSelectedSource] = useState(null);
  const [recordedBlob, setRecordedBlob] = useState(null);
  const [permissions, setPermissions] = useState({});
  const [status, setStatus] = useState('Ready to record');
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [showExportOptions, setShowExportOptions] = useState(false);

  // New Phase 2 state variables
  const [recordingSettings, setRecordingSettings] = useState({
    includeSystemAudio: false,
    includeMicrophone: true,
    includeWebcam: false,
    resolution: '1920x1080',
    frameRate: 30,
    quality: 'high',
    selectedMicrophone: 'default',
    selectedCamera: 'default',
    selectedSpeaker: 'default'
  });
  const [webcamStream, setWebcamStream] = useState(null);
  const [countdown, setCountdown] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [audioLevels, setAudioLevels] = useState({ microphone: 0, system: 0 });
  const [tempSettings, setTempSettings] = useState(null);
  const [availableDevices, setAvailableDevices] = useState({
    microphones: [],
    cameras: [],
    speakers: []
  });

  // Phase 3: Smart Zoom & Cursor Tracking state
  const [cursorTracking, setCursorTracking] = useState({
    enabled: true,
    positions: [],
    clicks: [],
    currentZoom: { x: 0, y: 0, scale: 1 },
    isZooming: false
  });

  const videoRef = useRef(null);
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const streamRef = useRef(null);
  const durationIntervalRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const cursorTrackingIntervalRef = useRef(null);
  const zoomCanvasRef = useRef(null);
  
  useEffect(() => {
    checkPermissions();
    loadSources();
    loadAvailableDevices();
  }, []);

  const loadAvailableDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();

      const microphones = devices
        .filter(device => device.kind === 'audioinput')
        .map(device => ({
          id: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,
          groupId: device.groupId
        }));

      const cameras = devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          id: device.deviceId,
          label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,
          groupId: device.groupId
        }));

      const speakers = devices
        .filter(device => device.kind === 'audiooutput')
        .map(device => ({
          id: device.deviceId,
          label: device.label || `Speaker ${device.deviceId.slice(0, 8)}`,
          groupId: device.groupId
        }));

      setAvailableDevices({
        microphones: [
          { id: 'default', label: 'Default Microphone', groupId: null },
          ...microphones
        ],
        cameras: [
          { id: 'default', label: 'Default Camera', groupId: null },
          ...cameras
        ],
        speakers: [
          { id: 'default', label: 'Default Speaker', groupId: null },
          ...speakers
        ]
      });
    } catch (error) {
      console.warn('Failed to enumerate devices:', error);
      // Set default options if enumeration fails
      setAvailableDevices({
        microphones: [{ id: 'default', label: 'Default Microphone', groupId: null }],
        cameras: [{ id: 'default', label: 'Default Camera', groupId: null }],
        speakers: [{ id: 'default', label: 'Default Speaker', groupId: null }]
      });
    }
  };
  
  const checkPermissions = async () => {
    const perms = await window.electronAPI.checkPermissions();
    setPermissions(perms);

    // Show status message about permissions
    const permissionStatus = [];
    if (perms.screen === 'granted') permissionStatus.push('✅ Screen recording');
    else permissionStatus.push('❌ Screen recording');

    if (perms.microphone === 'granted') permissionStatus.push('✅ Microphone');
    else permissionStatus.push('❌ Microphone');

    if (perms.camera === 'granted') permissionStatus.push('✅ Camera');
    else permissionStatus.push('❌ Camera');

    setStatus(`Permissions: ${permissionStatus.join(', ')}`);

    // Reset status after 3 seconds
    setTimeout(() => {
      if (!isRecording && !recordedBlob) {
        setStatus('Ready to record');
      }
    }, 3000);
  };
  
  const requestPermissions = async () => {
    const granted = await window.electronAPI.requestPermissions();
    if (granted) {
      await checkPermissions();
    }
  };
  
  const loadSources = async () => {
    const sources = await window.electronAPI.getSources();
    setSources(sources);
    if (sources.length > 0 && !selectedSource) {
      // Auto-select the first screen
      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));
      setSelectedSource(screen || sources[0]);
    }
  };
  
  const startRecording = async () => {
    if (!selectedSource) {
      setStatus('Please select a screen or window to record');
      return;
    }

    try {
      // Start countdown if enabled
      if (countdown > 0) {
        await startCountdown();
      }

      setStatus('Starting recording...');
      setRecordingDuration(0);

      // Start duration timer
      durationIntervalRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      // Parse resolution settings
      const [width, height] = recordingSettings.resolution.split('x').map(Number);

      // Get screen stream with system audio if enabled
      const screenConstraints = {
        audio: recordingSettings.includeSystemAudio ? {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: selectedSource.id,
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        } : false,
        video: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: selectedSource.id,
            minWidth: width,
            maxWidth: width,
            minHeight: height,
            maxHeight: height,
            minFrameRate: recordingSettings.frameRate,
            maxFrameRate: recordingSettings.frameRate
          }
        }
      };

      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);

      // Get microphone stream if enabled
      let microphoneStream = null;
      if (recordingSettings.includeMicrophone) {
        try {
          const audioConstraints = {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 48000
          };

          // Add device ID if not default
          if (recordingSettings.selectedMicrophone !== 'default') {
            audioConstraints.deviceId = { exact: recordingSettings.selectedMicrophone };
          }

          microphoneStream = await navigator.mediaDevices.getUserMedia({
            audio: audioConstraints,
            video: false
          });
        } catch (audioError) {
          console.warn('Microphone access denied, recording without microphone');
        }
      }

      // Get webcam stream if enabled
      let webcamStreamLocal = null;
      if (recordingSettings.includeWebcam) {
        try {
          const videoConstraints = {
            width: { ideal: 320 },
            height: { ideal: 240 },
            frameRate: { ideal: 30 }
          };

          // Add device ID if not default
          if (recordingSettings.selectedCamera !== 'default') {
            videoConstraints.deviceId = { exact: recordingSettings.selectedCamera };
          }

          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({
            video: videoConstraints,
            audio: false
          });
          setWebcamStream(webcamStreamLocal);

          // Display webcam preview
          if (webcamRef.current) {
            webcamRef.current.srcObject = webcamStreamLocal;
          }
        } catch (webcamError) {
          console.warn('Webcam access denied, recording without webcam');
        }
      }

      // Combine all streams
      let combinedStream = screenStream;

      // Add microphone audio if available
      if (microphoneStream) {
        const audioTracks = microphoneStream.getAudioTracks();
        audioTracks.forEach(track => combinedStream.addTrack(track));

        // Setup audio level monitoring
        setupAudioMonitoring(microphoneStream);
      }

      // Note: Webcam video will be composited in post-processing for now
      // In a future version, we could use Canvas API to composite in real-time

      streamRef.current = combinedStream;

      // Set up MediaRecorder with quality settings
      const mimeType = getOptimalMimeType();
      const mediaRecorder = new MediaRecorder(combinedStream, {
        mimeType,
        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)
      });

      const chunks = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: mimeType });
        setRecordedBlob(blob);
        setStatus('Recording saved. You can now download it.');

        // Clean up webcam stream
        if (webcamStreamLocal) {
          webcamStreamLocal.getTracks().forEach(track => track.stop());
          setWebcamStream(null);
        }

        // Show preview - use setTimeout to ensure the video element is rendered
        setTimeout(() => {
          if (videoRef.current && blob) {
            const videoUrl = URL.createObjectURL(blob);
            videoRef.current.src = videoUrl;
            videoRef.current.load(); // Force reload of the video element
          }
        }, 100);
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start(100); // Collect data every 100ms

      setIsRecording(true);
      setStatus('Recording in progress');

      // Start cursor tracking for smart zoom
      startCursorTracking();

    } catch (error) {
      console.error('Failed to start recording:', error);
      setStatus('Failed to start recording. Please check permissions.');
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();

      // Stop duration timer
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }

      // Stop all tracks
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }

      // Stop webcam stream
      if (webcamStream) {
        webcamStream.getTracks().forEach(track => track.stop());
        setWebcamStream(null);
      }

      // Clean up audio context
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
        analyserRef.current = null;
      }

      // Reset audio levels
      setAudioLevels({ microphone: 0, system: 0 });

      // Stop cursor tracking
      stopCursorTracking();

      setIsRecording(false);
      setStatus('Processing recording...');
    }
  };
  
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Helper functions for Phase 2 features
  const startCountdown = () => {
    return new Promise((resolve) => {
      let count = countdown;
      setStatus(`Recording starts in ${count}...`);

      const countdownInterval = setInterval(() => {
        count--;
        if (count > 0) {
          setStatus(`Recording starts in ${count}...`);
        } else {
          setStatus('Recording starting...');
          clearInterval(countdownInterval);
          resolve();
        }
      }, 1000);
    });
  };

  const getOptimalMimeType = () => {
    const types = [
      'video/webm;codecs=vp9,opus',
      'video/webm;codecs=vp8,opus',
      'video/webm;codecs=h264,opus',
      'video/webm'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }
    return 'video/webm';
  };

  const getVideoBitrate = (quality, width, height) => {
    const pixelCount = width * height;
    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels

    switch (quality) {
      case 'draft':
        return Math.floor(baseRate * 0.5) * 1000; // 0.5 bits per pixel
      case 'standard':
        return Math.floor(baseRate * 1) * 1000; // 1 bit per pixel
      case 'high':
        return Math.floor(baseRate * 2) * 1000; // 2 bits per pixel
      default:
        return Math.floor(baseRate * 1) * 1000;
    }
  };

  const setupAudioMonitoring = (audioStream) => {
    if (!audioStream) return;

    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const source = audioContext.createMediaStreamSource(audioStream);

      analyser.fftSize = 256;
      source.connect(analyser);

      audioContextRef.current = audioContext;
      analyserRef.current = analyser;

      // Start monitoring audio levels
      const monitorAudio = () => {
        if (!analyserRef.current) return;

        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
        analyserRef.current.getByteFrequencyData(dataArray);

        // Calculate average volume
        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
        const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1

        setAudioLevels(prev => ({
          ...prev,
          microphone: normalizedLevel
        }));

        if (isRecording) {
          requestAnimationFrame(monitorAudio);
        }
      };

      monitorAudio();
    } catch (error) {
      console.warn('Audio monitoring setup failed:', error);
    }
  };

  // Settings modal functions
  const openSettings = () => {
    setTempSettings({ ...recordingSettings, countdown });
    setShowSettings(true);
    // Refresh device list when opening settings
    loadAvailableDevices();
  };

  const closeSettings = () => {
    setShowSettings(false);
    setTempSettings(null);
  };

  const saveSettings = () => {
    if (tempSettings) {
      setRecordingSettings({
        includeSystemAudio: tempSettings.includeSystemAudio,
        includeMicrophone: tempSettings.includeMicrophone,
        includeWebcam: tempSettings.includeWebcam,
        resolution: tempSettings.resolution,
        frameRate: tempSettings.frameRate,
        quality: tempSettings.quality,
        selectedMicrophone: tempSettings.selectedMicrophone,
        selectedCamera: tempSettings.selectedCamera,
        selectedSpeaker: tempSettings.selectedSpeaker
      });
      setCountdown(tempSettings.countdown);
    }
    setShowSettings(false);
    setTempSettings(null);
  };

  // Phase 3: Cursor Tracking Functions
  const startCursorTracking = () => {
    if (!cursorTracking.enabled) return;

    // Clear any existing tracking
    stopCursorTracking();

    // Track mouse movements and clicks
    const trackMouseMovement = (event) => {
      const timestamp = Date.now();
      const position = {
        x: event.screenX,
        y: event.screenY,
        timestamp,
        velocity: calculateVelocity(event.screenX, event.screenY, timestamp)
      };

      setCursorTracking(prev => ({
        ...prev,
        positions: [...prev.positions.slice(-99), position] // Keep last 100 positions
      }));
    };

    const trackMouseClick = (event) => {
      const timestamp = Date.now();
      const click = {
        x: event.screenX,
        y: event.screenY,
        timestamp,
        button: event.button,
        type: event.type
      };

      setCursorTracking(prev => ({
        ...prev,
        clicks: [...prev.clicks.slice(-49), click] // Keep last 50 clicks
      }));

      // Analyze if this click should trigger a zoom
      analyzeZoomTrigger(click);
    };

    // Add global event listeners
    document.addEventListener('mousemove', trackMouseMovement);
    document.addEventListener('click', trackMouseClick);
    document.addEventListener('mousedown', trackMouseClick);
    document.addEventListener('mouseup', trackMouseClick);

    // Store cleanup function
    cursorTrackingIntervalRef.current = () => {
      document.removeEventListener('mousemove', trackMouseMovement);
      document.removeEventListener('click', trackMouseClick);
      document.removeEventListener('mousedown', trackMouseClick);
      document.removeEventListener('mouseup', trackMouseClick);
    };
  };

  const stopCursorTracking = () => {
    if (cursorTrackingIntervalRef.current) {
      cursorTrackingIntervalRef.current();
      cursorTrackingIntervalRef.current = null;
    }
  };

  const calculateVelocity = (x, y, timestamp) => {
    const positions = cursorTracking.positions;
    if (positions.length === 0) return 0;

    const lastPosition = positions[positions.length - 1];
    const deltaX = x - lastPosition.x;
    const deltaY = y - lastPosition.y;
    const deltaTime = timestamp - lastPosition.timestamp;

    if (deltaTime === 0) return 0;

    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    return distance / deltaTime; // pixels per millisecond
  };

  const analyzeZoomTrigger = (click) => {
    // Simple zoom trigger logic - will enhance this
    const recentActivity = calculateRecentActivity();
    const shouldZoom = recentActivity.clickDensity > 0.5 || recentActivity.movementIntensity > 0.3;

    if (shouldZoom && !cursorTracking.isZooming) {
      triggerSmartZoom(click.x, click.y);
    }
  };

  const calculateRecentActivity = () => {
    const now = Date.now();
    const timeWindow = 2000; // 2 seconds

    const recentClicks = cursorTracking.clicks.filter(
      click => now - click.timestamp < timeWindow
    );

    const recentPositions = cursorTracking.positions.filter(
      pos => now - pos.timestamp < timeWindow
    );

    const clickDensity = recentClicks.length / (timeWindow / 1000); // clicks per second
    const movementIntensity = recentPositions.reduce((sum, pos) => sum + pos.velocity, 0) / recentPositions.length;

    return { clickDensity, movementIntensity };
  };

  const triggerSmartZoom = (targetX, targetY) => {
    setCursorTracking(prev => ({
      ...prev,
      isZooming: true,
      currentZoom: {
        x: targetX,
        y: targetY,
        scale: 1.5 // 1.5x zoom
      }
    }));

    // Reset zoom after 3 seconds
    setTimeout(() => {
      setCursorTracking(prev => ({
        ...prev,
        isZooming: false,
        currentZoom: { x: 0, y: 0, scale: 1 }
      }));
    }, 3000);
  };

  const downloadRecording = async (format = 'webm') => {
    if (recordedBlob) {
      try {
        if (format === 'webm') {
          // Direct WebM download
          const url = URL.createObjectURL(recordedBlob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
          a.click();
          URL.revokeObjectURL(url);
          setShowExportOptions(false);
        } else if (format === 'mp4') {
          setStatus('Converting to MP4...');

          // Convert blob to array buffer (browser-compatible)
          const arrayBuffer = await recordedBlob.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);

          // Save and convert via Electron
          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);

          if (savedPath) {
            setStatus('Recording saved as MP4!');
            setShowExportOptions(false);
          }
        }
      } catch (error) {
        console.error('Failed to save recording:', error);
        setStatus('Failed to save recording');
      }
    }
  };
  
  const clearRecording = () => {
    // Clean up blob URL to prevent memory leaks
    if (videoRef.current && videoRef.current.src) {
      URL.revokeObjectURL(videoRef.current.src);
      videoRef.current.src = '';
    }
    setRecordedBlob(null);
    setStatus('Ready to record');
  };
  
  return (
    <div className="app">
      <header className="header">
        <h1>Screen Recorder</h1>
        <div className="header-controls">
          <button
            className="btn btn-secondary"
            onClick={checkPermissions}
          >
            🔒 Check Permissions
          </button>
          <button
            className="btn btn-secondary"
            onClick={openSettings}
            disabled={isRecording}
          >
            ⚙️ Settings
          </button>
          {permissions.screen === 'denied' && (
            <button className="btn btn-primary" onClick={requestPermissions}>
              Grant Permissions
            </button>
          )}
        </div>
      </header>

      {showSettings && (
        <div className="modal-overlay" onClick={closeSettings}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Recording Settings</h3>
              <button className="modal-close" onClick={closeSettings}>×</button>
            </div>

            <div className="modal-body">
              <div className="settings-group">
                <h4>Audio</h4>
                <label className="setting-item">
                  <input
                    type="checkbox"
                    checked={tempSettings?.includeMicrophone || false}
                    onChange={(e) => setTempSettings(prev => ({
                      ...prev,
                      includeMicrophone: e.target.checked
                    }))}
                  />
                  Include Microphone
                </label>

                {tempSettings?.includeMicrophone && (
                  <div className="setting-item device-selector">
                    <label>Microphone Source:</label>
                    <select
                      value={tempSettings?.selectedMicrophone || 'default'}
                      onChange={(e) => setTempSettings(prev => ({
                        ...prev,
                        selectedMicrophone: e.target.value
                      }))}
                    >
                      {availableDevices.microphones.map(device => (
                        <option key={device.id} value={device.id}>
                          {device.label}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <label className="setting-item">
                  <input
                    type="checkbox"
                    checked={tempSettings?.includeSystemAudio || false}
                    onChange={(e) => setTempSettings(prev => ({
                      ...prev,
                      includeSystemAudio: e.target.checked
                    }))}
                  />
                  Include System Audio
                  <span className="feature-note">(Phase 3 - Experimental)</span>
                </label>
              </div>

              <div className="settings-group">
                <h4>Video</h4>
                <label className="setting-item">
                  <input
                    type="checkbox"
                    checked={tempSettings?.includeWebcam || false}
                    onChange={(e) => setTempSettings(prev => ({
                      ...prev,
                      includeWebcam: e.target.checked
                    }))}
                  />
                  Include Webcam
                </label>

                {tempSettings?.includeWebcam && (
                  <div className="setting-item device-selector">
                    <label>Camera Source:</label>
                    <select
                      value={tempSettings?.selectedCamera || 'default'}
                      onChange={(e) => setTempSettings(prev => ({
                        ...prev,
                        selectedCamera: e.target.value
                      }))}
                    >
                      {availableDevices.cameras.map(device => (
                        <option key={device.id} value={device.id}>
                          {device.label}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div className="setting-item">
                  <label>Resolution:</label>
                  <select
                    value={tempSettings?.resolution || '1920x1080'}
                    onChange={(e) => setTempSettings(prev => ({
                      ...prev,
                      resolution: e.target.value
                    }))}
                  >
                    <option value="1280x720">720p (1280x720)</option>
                    <option value="1920x1080">1080p (1920x1080)</option>
                    <option value="2560x1440">1440p (2560x1440)</option>
                    <option value="3840x2160">4K (3840x2160)</option>
                  </select>
                </div>

                <div className="setting-item">
                  <label>Frame Rate:</label>
                  <select
                    value={tempSettings?.frameRate || 30}
                    onChange={(e) => setTempSettings(prev => ({
                      ...prev,
                      frameRate: parseInt(e.target.value)
                    }))}
                  >
                    <option value={30}>30 FPS</option>
                    <option value={60}>60 FPS</option>
                  </select>
                </div>

                <div className="setting-item">
                  <label>Quality:</label>
                  <select
                    value={tempSettings?.quality || 'high'}
                    onChange={(e) => setTempSettings(prev => ({
                      ...prev,
                      quality: e.target.value
                    }))}
                  >
                    <option value="draft">Draft (Smaller file)</option>
                    <option value="standard">Standard</option>
                    <option value="high">High Quality</option>
                  </select>
                </div>
              </div>

              <div className="settings-group">
                <h4>Recording</h4>
                <div className="setting-item">
                  <label>Countdown Timer (seconds):</label>
                  <select
                    value={tempSettings?.countdown || 0}
                    onChange={(e) => setTempSettings(prev => ({
                      ...prev,
                      countdown: parseInt(e.target.value)
                    }))}
                  >
                    <option value={0}>No countdown</option>
                    <option value={3}>3 seconds</option>
                    <option value={5}>5 seconds</option>
                    <option value={10}>10 seconds</option>
                  </select>
                </div>
              </div>

              <div className="settings-group">
                <h4>Smart Zoom (Phase 3)</h4>
                <label className="setting-item">
                  <input
                    type="checkbox"
                    checked={cursorTracking.enabled}
                    onChange={(e) => setCursorTracking(prev => ({
                      ...prev,
                      enabled: e.target.checked
                    }))}
                  />
                  Enable Smart Zoom
                  <span className="feature-note">(Automatically zoom to cursor activity)</span>
                </label>

                {cursorTracking.enabled && (
                  <div className="zoom-preview">
                    <div className="zoom-status">
                      {cursorTracking.isZooming ? (
                        <span className="zoom-active">🔍 Zooming Active</span>
                      ) : (
                        <span className="zoom-ready">👁️ Watching for Activity</span>
                      )}
                    </div>
                    <div className="zoom-stats">
                      <small>
                        Tracked: {cursorTracking.positions.length} movements, {cursorTracking.clicks.length} clicks
                      </small>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={loadAvailableDevices}>
                🔄 Refresh Devices
              </button>
              <div className="modal-footer-right">
                <button className="btn btn-secondary" onClick={closeSettings}>
                  Cancel
                </button>
                <button className="btn btn-primary" onClick={saveSettings}>
                  Save Settings
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <main className="content">
        <div className="source-selector">
          <h3>Select Screen or Window:</h3>
          <div className="sources-grid">
            {sources.map((source) => (
              <div
                key={source.id}
                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}
                onClick={() => setSelectedSource(source)}
              >
                <img
                  src={source.thumbnail}
                  alt={source.name}
                  className="source-thumbnail"
                />
                <div className="source-name">{source.name}</div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="recording-area">
          {recordedBlob ? (
            <div className="preview-container">
              <video
                ref={videoRef}
                className="preview-video"
                controls
                autoPlay={false}
                preload="metadata"
                muted
                playsInline
              />
              <div className="recording-info">
                <p>Recording completed • Duration: {formatDuration(recordingDuration)}</p>
              </div>
            </div>
          ) : (
            <div className="status-display">
              <div className={`status-box ${isRecording ? 'recording' : ''}`}>
                {isRecording ? (
                  <div className="recording-status">
                    <div className="recording-indicator">
                      <span className="recording-dot"></span>
                      RECORDING
                    </div>
                    <div className="recording-duration">
                      {formatDuration(recordingDuration)}
                    </div>
                    <div className="recording-source">
                      {selectedSource?.name}
                    </div>
                    <div className="recording-settings-info">
                      {recordingSettings.resolution} • {recordingSettings.frameRate}fps • {recordingSettings.quality}
                      {cursorTracking.enabled && (
                        <span className="smart-zoom-indicator">
                          {cursorTracking.isZooming ? ' • 🔍 Smart Zoom Active' : ' • 👁️ Smart Zoom Ready'}
                        </span>
                      )}
                    </div>

                    {/* Audio level indicators */}
                    {(recordingSettings.includeMicrophone || recordingSettings.includeSystemAudio) && (
                      <div className="audio-levels">
                        {recordingSettings.includeMicrophone && (
                          <div className="audio-level-item">
                            <span className="audio-label">🎤</span>
                            <div className="audio-meter">
                              <div
                                className="audio-level-bar"
                                style={{ width: `${audioLevels.microphone * 100}%` }}
                              />
                            </div>
                          </div>
                        )}
                        {recordingSettings.includeSystemAudio && (
                          <div className="audio-level-item">
                            <span className="audio-label">🔊</span>
                            <div className="audio-meter">
                              <div
                                className="audio-level-bar"
                                style={{ width: `${audioLevels.system * 100}%` }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="ready-status">
                    <div className="status-text">{status}</div>
                    {selectedSource && (
                      <div className="selected-source">Ready to record: {selectedSource.name}</div>
                    )}
                  </div>
                )}
              </div>

              {/* Webcam preview during recording */}
              {webcamStream && isRecording && (
                <div className="webcam-preview">
                  <video
                    ref={webcamRef}
                    className="webcam-video"
                    autoPlay
                    muted
                    playsInline
                  />
                  <div className="webcam-label">Webcam</div>
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className="controls">
          {!isRecording && !recordedBlob && (
            <button
              className="btn btn-primary"
              onClick={startRecording}
              disabled={!selectedSource || permissions.screen === 'denied'}
            >
              Start Recording
            </button>
          )}
          
          {isRecording && (
            <button className="btn btn-danger" onClick={stopRecording}>
              Stop Recording
            </button>
          )}
          
          {recordedBlob && !isRecording && (
            <>
              {!showExportOptions ? (
                <button 
                  className="btn btn-primary" 
                  onClick={() => setShowExportOptions(true)}
                >
                  Download Recording
                </button>
              ) : (
                <div className="export-options">
                  <h4>Choose format:</h4>
                  <button 
                    className="btn btn-primary" 
                    onClick={() => downloadRecording('webm')}
                  >
                    Download WebM (Fast)
                  </button>
                  <button 
                    className="btn btn-primary" 
                    onClick={() => downloadRecording('mp4')}
                  >
                    Download MP4 (Compatible)
                  </button>
                  <button 
                    className="btn btn-secondary" 
                    onClick={() => setShowExportOptions(false)}
                  >
                    Cancel
                  </button>
                </div>
              )}
              <button className="btn btn-secondary" onClick={clearRecording}>
                New Recording
              </button>
            </>
          )}
          
          <button className="btn btn-secondary" onClick={loadSources}>
            Refresh Sources
          </button>
        </div>
      </main>
    </div>
  );
}

export default App;
