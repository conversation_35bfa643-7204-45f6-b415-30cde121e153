* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #1a1a1a;
  color: white;
  height: 100vh;
  overflow: hidden;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.header {
  padding: 16px 24px;
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.recording-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #252525;
  border: 2px dashed #4a4a4a;
  border-radius: 12px;
  margin-bottom: 24px;
}

.controls {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #007aff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056cc;
}

.btn-danger {
  background: #ff3b30;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #cc2e24;
}

.btn-secondary {
  background: #3a3a3a;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4a4a4a;
}

.status {
  margin: 16px 0;
  text-align: center;
  font-size: 16px;
}

.status.recording {
  color: #ff3b30;
}

.preview-video {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
}

.source-selector {
  margin-bottom: 16px;
}

.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.source-item {
  padding: 12px;
  background: #3a3a3a;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.source-item:hover {
  background: #4a4a4a;
}

.source-item.selected {
  background: #007aff;
}

.source-thumbnail {
  width: 150px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 8px;
}

.source-name {
  font-size: 12px;
  word-break: break-word;
}

.status-display {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.status-box {
  background: #2a2a2a;
  border: 2px solid #444;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  min-width: 300px;
}

.status-box.recording {
  border-color: #ff4444;
  background: #2a1a1a;
}

.recording-status {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recording-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-weight: bold;
  color: #ff4444;
  font-size: 18px;
}

.recording-dot {
  width: 12px;
  height: 12px;
  background: #ff4444;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

.recording-duration {
  font-size: 32px;
  font-weight: bold;
  color: #fff;
  font-family: 'Courier New', monospace;
}

.recording-source {
  color: #ccc;
  font-size: 14px;
}

.ready-status .status-text {
  font-size: 18px;
  color: #fff;
  margin-bottom: 10px;
}

.selected-source {
  color: #4CAF50;
  font-size: 14px;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px;
  background: #2a2a2a;
  border-radius: 8px;
  margin: 10px 0;
}

.export-options h4 {
  margin: 0 0 10px 0;
  color: #fff;
}

/* Phase 2 Styles - Settings Groups (used in modal) */

.settings-group {
  margin-bottom: 24px;
}

.settings-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #ccc;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.setting-item label {
  font-size: 14px;
  color: #ddd;
  min-width: 100px;
}

.setting-item input[type="checkbox"] {
  margin-right: 8px;
}

.setting-item select {
  background: #3a3a3a;
  border: 1px solid #4a4a4a;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  min-width: 150px;
}

.setting-item select:focus {
  outline: none;
  border-color: #007acc;
}

.recording-settings-info {
  font-size: 12px;
  color: #888;
  margin-top: 8px;
}

.webcam-preview {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #007acc;
}

.webcam-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.webcam-label {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.preview-container {
  position: relative;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #2a2a2a;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #3a3a3a;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.modal-close {
  background: none;
  border: none;
  color: #ccc;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background: #3a3a3a;
  color: #fff;
}

.modal-body {
  padding: 20px 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #3a3a3a;
  background: #252525;
}

/* Audio Level Indicators */
.audio-levels {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.audio-level-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.audio-label {
  font-size: 16px;
  width: 20px;
}

.audio-meter {
  flex: 1;
  height: 6px;
  background: #3a3a3a;
  border-radius: 3px;
  overflow: hidden;
  max-width: 200px;
}

.audio-level-bar {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #FFC107 70%, #F44336 100%);
  transition: width 0.1s ease;
  border-radius: 3px;
}

.recording-info {
  text-align: center;
  margin-top: 10px;
  color: #ccc;
}
