export interface ElectronAPI {
  getSources: () => Promise<Array<{
    id: string;
    name: string;
    thumbnail: string;
  }>>;
  getAudioSources: () => Promise<Array<{
    id: string;
    name: string;
    type: string;
  }>>;
  checkPermissions: () => Promise<{
    screen?: string;
    microphone?: string;
    camera?: string;
  }>;
  requestPermissions: () => Promise<boolean>;
  saveRecording: (buffer: Uint8Array, filename: string) => Promise<string>;
  convertToMp4: (webmPath: string, outputPath: string) => Promise<string>;
  platform: string;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    webkitAudioContext: typeof AudioContext;
  }
}
