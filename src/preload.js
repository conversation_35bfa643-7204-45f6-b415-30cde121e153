const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // Screen recording APIs
  getSources: () => ipcRenderer.invoke('get-sources'),
  checkPermissions: () => ipcRenderer.invoke('check-permissions'),
  requestPermissions: () => ipcRenderer.invoke('request-permissions'),
  
  // File operations
  saveRecording: (buffer, filename) => ipcRenderer.invoke('save-recording', buffer, filename),
  convertToMp4: (webmPath, outputPath) => ipcRenderer.invoke('convert-to-mp4', webmPath, outputPath),
  
  // App info
  platform: process.platform
});
