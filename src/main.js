const { app, BrowserWindow, ipcMain, desktopCapturer, systemPreferences } = require('electron');
const path = require('path');
const isDev = process.argv.includes('--dev');
const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs');

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false // Allow access to getUserMedia
    },
    titleBarStyle: 'hiddenInset',
    show: false
  });

  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../build/index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  // Request screen recording permissions on macOS
  if (process.platform === 'darwin') {
    systemPreferences.getMediaAccessStatus('screen');
  }
  
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers for screen recording
ipcMain.handle('get-sources', async () => {
  try {
    const sources = await desktopCapturer.getSources({
      types: ['window', 'screen'],
      thumbnailSize: { width: 150, height: 150 }
    });
    
    return sources.map(source => ({
      id: source.id,
      name: source.name,
      thumbnail: source.thumbnail.toDataURL()
    }));
  } catch (error) {
    console.error('Failed to get sources:', error);
    return [];
  }
});

ipcMain.handle('check-permissions', async () => {
  if (process.platform === 'darwin') {
    const screenAccess = systemPreferences.getMediaAccessStatus('screen');
    const microphoneAccess = systemPreferences.getMediaAccessStatus('microphone');
    const cameraAccess = systemPreferences.getMediaAccessStatus('camera');
    
    return {
      screen: screenAccess,
      microphone: microphoneAccess,
      camera: cameraAccess
    };
  }
  
  return {
    screen: 'granted',
    microphone: 'granted', 
    camera: 'granted'
  };
});

ipcMain.handle('request-permissions', async () => {
  if (process.platform === 'darwin') {
    try {
      await systemPreferences.askForMediaAccess('microphone');
      await systemPreferences.askForMediaAccess('camera');
      return true;
    } catch (error) {
      console.error('Permission request failed:', error);
      return false;
    }
  }
  return true;
});

ipcMain.handle('convert-to-mp4', async (event, webmPath, outputPath) => {
  return new Promise((resolve, reject) => {
    ffmpeg(webmPath)
      .output(outputPath)
      .videoCodec('libx264')
      .audioCodec('aac')
      .format('mp4')
      .on('end', () => {
        // Clean up temporary WebM file
        fs.unlink(webmPath, (err) => {
          if (err) console.warn('Failed to delete temp file:', err);
        });
        resolve(outputPath);
      })
      .on('error', (err) => {
        reject(err);
      })
      .run();
  });
});

ipcMain.handle('save-recording', async (event, buffer, filename) => {
  const { dialog } = require('electron');
  
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      defaultPath: filename.replace('.webm', '.mp4'),
      filters: [
        { name: 'MP4 Videos', extensions: ['mp4'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });
    
    if (!result.canceled) {
      // Save as temporary WebM first
      const tempPath = path.join(__dirname, 'temp_recording.webm');
      fs.writeFileSync(tempPath, buffer);
      
      // Convert to MP4
      const mp4Path = await ipcMain.handle('convert-to-mp4', null, tempPath, result.filePath);
      return mp4Path;
    }
  } catch (error) {
    console.error('Failed to save recording:', error);
    throw error;
  }
});
