{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js\",\n  _s = $RefreshSig$();\n/// <reference path=\"./electron.d.ts\" />\nimport { useState, useRef, useEffect } from 'react';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n\n  // New Phase 2 state variables\n  const [recordingSettings, setRecordingSettings] = useState({\n    includeSystemAudio: false,\n    includeMicrophone: true,\n    includeWebcam: false,\n    resolution: '1920x1080',\n    frameRate: 30,\n    quality: 'high'\n  });\n  const [webcamStream, setWebcamStream] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [audioLevels, setAudioLevels] = useState({\n    microphone: 0,\n    system: 0\n  });\n  const videoRef = useRef(null);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    try {\n      // Start countdown if enabled\n      if (countdown > 0) {\n        await startCountdown();\n      }\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Parse resolution settings\n      const [width, height] = recordingSettings.resolution.split('x').map(Number);\n\n      // Get screen stream with enhanced settings\n      const screenConstraints = {\n        audio: recordingSettings.includeSystemAudio ? {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id\n          }\n        } : false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: width,\n            maxWidth: width,\n            minHeight: height,\n            maxHeight: height,\n            minFrameRate: recordingSettings.frameRate,\n            maxFrameRate: recordingSettings.frameRate\n          }\n        }\n      };\n      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);\n\n      // Get microphone stream if enabled\n      let microphoneStream = null;\n      if (recordingSettings.includeMicrophone) {\n        try {\n          microphoneStream = await navigator.mediaDevices.getUserMedia({\n            audio: {\n              echoCancellation: true,\n              noiseSuppression: true,\n              autoGainControl: true,\n              sampleRate: 48000\n            },\n            video: false\n          });\n        } catch (audioError) {\n          console.warn('Microphone access denied, recording without microphone');\n        }\n      }\n\n      // Get webcam stream if enabled\n      let webcamStreamLocal = null;\n      if (recordingSettings.includeWebcam) {\n        try {\n          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({\n            video: {\n              width: {\n                ideal: 320\n              },\n              height: {\n                ideal: 240\n              },\n              frameRate: {\n                ideal: 30\n              }\n            },\n            audio: false\n          });\n          setWebcamStream(webcamStreamLocal);\n\n          // Display webcam preview\n          if (webcamRef.current) {\n            webcamRef.current.srcObject = webcamStreamLocal;\n          }\n        } catch (webcamError) {\n          console.warn('Webcam access denied, recording without webcam');\n        }\n      }\n\n      // Combine all streams\n      let combinedStream = screenStream;\n\n      // Add microphone audio if available\n      if (microphoneStream) {\n        const audioTracks = microphoneStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n\n        // Setup audio level monitoring\n        setupAudioMonitoring(microphoneStream);\n      }\n\n      // Note: Webcam video will be composited in post-processing for now\n      // In a future version, we could use Canvas API to composite in real-time\n\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder with quality settings\n      const mimeType = getOptimalMimeType();\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType,\n        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)\n      });\n      const chunks = [];\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, {\n          type: mimeType\n        });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Clean up webcam stream\n        if (webcamStreamLocal) {\n          webcamStreamLocal.getTracks().forEach(track => track.stop());\n          setWebcamStream(null);\n        }\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n\n      // Stop webcam stream\n      if (webcamStream) {\n        webcamStream.getTracks().forEach(track => track.stop());\n        setWebcamStream(null);\n      }\n\n      // Clean up audio context\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n        analyserRef.current = null;\n      }\n\n      // Reset audio levels\n      setAudioLevels({\n        microphone: 0,\n        system: 0\n      });\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Helper functions for Phase 2 features\n  const startCountdown = () => {\n    return new Promise(resolve => {\n      let count = countdown;\n      setStatus(`Recording starts in ${count}...`);\n      const countdownInterval = setInterval(() => {\n        count--;\n        if (count > 0) {\n          setStatus(`Recording starts in ${count}...`);\n        } else {\n          setStatus('Recording starting...');\n          clearInterval(countdownInterval);\n          resolve();\n        }\n      }, 1000);\n    });\n  };\n  const getOptimalMimeType = () => {\n    const types = ['video/webm;codecs=vp9,opus', 'video/webm;codecs=vp8,opus', 'video/webm;codecs=h264,opus', 'video/webm'];\n    for (const type of types) {\n      if (MediaRecorder.isTypeSupported(type)) {\n        return type;\n      }\n    }\n    return 'video/webm';\n  };\n  const getVideoBitrate = (quality, width, height) => {\n    const pixelCount = width * height;\n    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels\n\n    switch (quality) {\n      case 'draft':\n        return Math.floor(baseRate * 0.5) * 1000;\n      // 0.5 bits per pixel\n      case 'standard':\n        return Math.floor(baseRate * 1) * 1000;\n      // 1 bit per pixel\n      case 'high':\n        return Math.floor(baseRate * 2) * 1000;\n      // 2 bits per pixel\n      default:\n        return Math.floor(baseRate * 1) * 1000;\n    }\n  };\n  const setupAudioMonitoring = audioStream => {\n    if (!audioStream) return;\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const analyser = audioContext.createAnalyser();\n      const source = audioContext.createMediaStreamSource(audioStream);\n      analyser.fftSize = 256;\n      source.connect(analyser);\n      audioContextRef.current = audioContext;\n      analyserRef.current = analyser;\n\n      // Start monitoring audio levels\n      const monitorAudio = () => {\n        if (!analyserRef.current) return;\n        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\n        analyserRef.current.getByteFrequencyData(dataArray);\n\n        // Calculate average volume\n        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;\n        const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1\n\n        setAudioLevels(prev => ({\n          ...prev,\n          microphone: normalizedLevel\n        }));\n        if (isRecording) {\n          requestAnimationFrame(monitorAudio);\n        }\n      };\n      monitorAudio();\n    } catch (error) {\n      console.warn('Audio monitoring setup failed:', error);\n    }\n  };\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Screen Recorder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => setShowSettings(!showSettings),\n          disabled: isRecording,\n          children: \"\\u2699\\uFE0F Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), permissions.screen === 'denied' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: requestPermissions,\n          children: \"Grant Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Recording Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Audio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: recordingSettings.includeMicrophone,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              includeMicrophone: e.target.checked\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this), \"Include Microphone\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: recordingSettings.includeSystemAudio,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              includeSystemAudio: e.target.checked\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this), \"Include System Audio\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Video\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: recordingSettings.includeWebcam,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              includeWebcam: e.target.checked\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), \"Include Webcam\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Resolution:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: recordingSettings.resolution,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              resolution: e.target.value\n            })),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"1280x720\",\n              children: \"720p (1280x720)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"1920x1080\",\n              children: \"1080p (1920x1080)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"2560x1440\",\n              children: \"1440p (2560x1440)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"3840x2160\",\n              children: \"4K (3840x2160)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Frame Rate:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: recordingSettings.frameRate,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              frameRate: parseInt(e.target.value)\n            })),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: 30,\n              children: \"30 FPS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 60,\n              children: \"60 FPS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Quality:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: recordingSettings.quality,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              quality: e.target.value\n            })),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"draft\",\n              children: \"Draft (Smaller file)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"standard\",\n              children: \"Standard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"high\",\n              children: \"High Quality\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Countdown Timer (seconds):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: countdown,\n            onChange: e => setCountdown(parseInt(e.target.value)),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: 0,\n              children: \"No countdown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 3,\n              children: \"3 seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 5,\n              children: \"5 seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 10,\n              children: \"10 seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"source-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select Screen or Window:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sources-grid\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `source-item ${(selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.id) === source.id ? 'selected' : ''}`,\n            onClick: () => setSelectedSource(source),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: source.thumbnail,\n              alt: source.name,\n              className: \"source-thumbnail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"source-name\",\n              children: source.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this)]\n          }, source.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-area\",\n        children: recordedBlob ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: videoRef,\n            className: \"preview-video\",\n            controls: true,\n            autoPlay: false,\n            preload: \"metadata\",\n            muted: true,\n            playsInline: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recording-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Recording completed \\u2022 Duration: \", formatDuration(recordingDuration)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status-box ${isRecording ? 'recording' : ''}`,\n            children: isRecording ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"recording-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"recording-dot\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 23\n                }, this), \"RECORDING\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-duration\",\n                children: formatDuration(recordingDuration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-source\",\n                children: selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-settings-info\",\n                children: [recordingSettings.resolution, \" \\u2022 \", recordingSettings.frameRate, \"fps \\u2022 \", recordingSettings.quality]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 21\n              }, this), (recordingSettings.includeMicrophone || recordingSettings.includeSystemAudio) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audio-levels\",\n                children: [recordingSettings.includeMicrophone && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audio-level-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audio-label\",\n                    children: \"\\uD83C\\uDFA4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audio-meter\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"audio-level-bar\",\n                      style: {\n                        width: `${audioLevels.microphone * 100}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 589,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 27\n                }, this), recordingSettings.includeSystemAudio && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audio-level-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audio-label\",\n                    children: \"\\uD83D\\uDD0A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audio-meter\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"audio-level-bar\",\n                      style: {\n                        width: `${audioLevels.system * 100}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ready-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-text\",\n                children: status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 21\n              }, this), selectedSource && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-source\",\n                children: [\"Ready to record: \", selectedSource.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), webcamStream && isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webcam-preview\",\n            children: [/*#__PURE__*/_jsxDEV(\"video\", {\n              ref: webcamRef,\n              className: \"webcam-video\",\n              autoPlay: true,\n              muted: true,\n              playsInline: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"webcam-label\",\n              children: \"Webcam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        children: [!isRecording && !recordedBlob && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: startRecording,\n          disabled: !selectedSource || permissions.screen === 'denied',\n          children: \"Start Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 13\n        }, this), isRecording && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger\",\n          onClick: stopRecording,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 13\n        }, this), recordedBlob && !isRecording && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [!showExportOptions ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowExportOptions(true),\n            children: \"Download Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"export-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Choose format:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('webm'),\n              children: \"Download WebM (Fast)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('mp4'),\n              children: \"Download MP4 (Compatible)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => setShowExportOptions(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearRecording,\n            children: \"New Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: loadSources,\n          children: \"Refresh Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 399,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OQMcbAUhJ+F68BMoDU6oXCubaA8=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isRecording", "setIsRecording", "sources", "setSources", "selectedSource", "setSelectedSource", "recordedBlob", "setRecordedBlob", "permissions", "setPermissions", "status", "setStatus", "recordingDuration", "setRecordingDuration", "showExportOptions", "setShowExportOptions", "recordingSettings", "setRecordingSettings", "includeSystemAudio", "includeMicrophone", "includeWebcam", "resolution", "frameRate", "quality", "webcamStream", "setWebcamStream", "countdown", "setCountdown", "showSettings", "setShowSettings", "audioLevels", "setAudioLevels", "microphone", "system", "videoRef", "webcamRef", "mediaRecorderRef", "streamRef", "durationIntervalRef", "audioContextRef", "analyserRef", "checkPermissions", "loadSources", "perms", "window", "electronAPI", "requestPermissions", "granted", "getSources", "length", "screen", "find", "s", "name", "includes", "startRecording", "startCountdown", "current", "setInterval", "prev", "width", "height", "split", "map", "Number", "screenConstraints", "audio", "mandatory", "chromeMediaSource", "chromeMediaSourceId", "id", "video", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "minFrameRate", "maxFrameRate", "screenStream", "navigator", "mediaDevices", "getUserMedia", "microphoneStream", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "audioError", "console", "warn", "webcamStreamLocal", "ideal", "srcObject", "webcamError", "combinedStream", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "addTrack", "setupAudioMonitoring", "mimeType", "getOptimalMimeType", "mediaRecorder", "MediaRecorder", "videoBitsPerSecond", "getVideoBitrate", "chunks", "ondataavailable", "event", "data", "size", "push", "onstop", "blob", "Blob", "type", "getTracks", "stop", "setTimeout", "videoUrl", "URL", "createObjectURL", "src", "load", "start", "error", "clearInterval", "stopRecording", "close", "formatDuration", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "Promise", "resolve", "count", "countdownInterval", "types", "isTypeSupported", "pixelCount", "baseRate", "audioStream", "audioContext", "AudioContext", "webkitAudioContext", "analyser", "create<PERSON><PERSON>yser", "source", "createMediaStreamSource", "fftSize", "connect", "monitorAudio", "dataArray", "Uint8Array", "frequencyBinCount", "getByteFrequencyData", "average", "reduce", "sum", "value", "normalizedLevel", "min", "requestAnimationFrame", "downloadRecording", "format", "url", "a", "document", "createElement", "href", "download", "Date", "toISOString", "slice", "replace", "click", "revokeObjectURL", "arrayBuffer", "uint8Array", "filename", "savedPath", "saveRecording", "clearRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "checked", "onChange", "e", "target", "parseInt", "thumbnail", "alt", "ref", "controls", "autoPlay", "preload", "muted", "playsInline", "style", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js"], "sourcesContent": ["/// <reference path=\"./electron.d.ts\" />\nimport { useState, useRef, useEffect } from 'react';\nimport './index.css';\n\nfunction App() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n\n  // New Phase 2 state variables\n  const [recordingSettings, setRecordingSettings] = useState({\n    includeSystemAudio: false,\n    includeMicrophone: true,\n    includeWebcam: false,\n    resolution: '1920x1080',\n    frameRate: 30,\n    quality: 'high'\n  });\n  const [webcamStream, setWebcamStream] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [audioLevels, setAudioLevels] = useState({ microphone: 0, system: 0 });\n\n  const videoRef = useRef(null);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  \n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  \n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  \n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  \n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  \n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n\n    try {\n      // Start countdown if enabled\n      if (countdown > 0) {\n        await startCountdown();\n      }\n\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Parse resolution settings\n      const [width, height] = recordingSettings.resolution.split('x').map(Number);\n\n      // Get screen stream with enhanced settings\n      const screenConstraints = {\n        audio: recordingSettings.includeSystemAudio ? {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id\n          }\n        } : false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: width,\n            maxWidth: width,\n            minHeight: height,\n            maxHeight: height,\n            minFrameRate: recordingSettings.frameRate,\n            maxFrameRate: recordingSettings.frameRate\n          }\n        }\n      };\n\n      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);\n\n      // Get microphone stream if enabled\n      let microphoneStream = null;\n      if (recordingSettings.includeMicrophone) {\n        try {\n          microphoneStream = await navigator.mediaDevices.getUserMedia({\n            audio: {\n              echoCancellation: true,\n              noiseSuppression: true,\n              autoGainControl: true,\n              sampleRate: 48000\n            },\n            video: false\n          });\n        } catch (audioError) {\n          console.warn('Microphone access denied, recording without microphone');\n        }\n      }\n\n      // Get webcam stream if enabled\n      let webcamStreamLocal = null;\n      if (recordingSettings.includeWebcam) {\n        try {\n          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({\n            video: {\n              width: { ideal: 320 },\n              height: { ideal: 240 },\n              frameRate: { ideal: 30 }\n            },\n            audio: false\n          });\n          setWebcamStream(webcamStreamLocal);\n\n          // Display webcam preview\n          if (webcamRef.current) {\n            webcamRef.current.srcObject = webcamStreamLocal;\n          }\n        } catch (webcamError) {\n          console.warn('Webcam access denied, recording without webcam');\n        }\n      }\n\n      // Combine all streams\n      let combinedStream = screenStream;\n\n      // Add microphone audio if available\n      if (microphoneStream) {\n        const audioTracks = microphoneStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n\n        // Setup audio level monitoring\n        setupAudioMonitoring(microphoneStream);\n      }\n\n      // Note: Webcam video will be composited in post-processing for now\n      // In a future version, we could use Canvas API to composite in real-time\n\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder with quality settings\n      const mimeType = getOptimalMimeType();\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType,\n        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)\n      });\n\n      const chunks = [];\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, { type: mimeType });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Clean up webcam stream\n        if (webcamStreamLocal) {\n          webcamStreamLocal.getTracks().forEach(track => track.stop());\n          setWebcamStream(null);\n        }\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  \n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n\n      // Stop webcam stream\n      if (webcamStream) {\n        webcamStream.getTracks().forEach(track => track.stop());\n        setWebcamStream(null);\n      }\n\n      // Clean up audio context\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n        analyserRef.current = null;\n      }\n\n      // Reset audio levels\n      setAudioLevels({ microphone: 0, system: 0 });\n\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  \n  const formatDuration = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Helper functions for Phase 2 features\n  const startCountdown = () => {\n    return new Promise((resolve) => {\n      let count = countdown;\n      setStatus(`Recording starts in ${count}...`);\n\n      const countdownInterval = setInterval(() => {\n        count--;\n        if (count > 0) {\n          setStatus(`Recording starts in ${count}...`);\n        } else {\n          setStatus('Recording starting...');\n          clearInterval(countdownInterval);\n          resolve();\n        }\n      }, 1000);\n    });\n  };\n\n  const getOptimalMimeType = () => {\n    const types = [\n      'video/webm;codecs=vp9,opus',\n      'video/webm;codecs=vp8,opus',\n      'video/webm;codecs=h264,opus',\n      'video/webm'\n    ];\n\n    for (const type of types) {\n      if (MediaRecorder.isTypeSupported(type)) {\n        return type;\n      }\n    }\n    return 'video/webm';\n  };\n\n  const getVideoBitrate = (quality, width, height) => {\n    const pixelCount = width * height;\n    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels\n\n    switch (quality) {\n      case 'draft':\n        return Math.floor(baseRate * 0.5) * 1000; // 0.5 bits per pixel\n      case 'standard':\n        return Math.floor(baseRate * 1) * 1000; // 1 bit per pixel\n      case 'high':\n        return Math.floor(baseRate * 2) * 1000; // 2 bits per pixel\n      default:\n        return Math.floor(baseRate * 1) * 1000;\n    }\n  };\n\n  const setupAudioMonitoring = (audioStream) => {\n    if (!audioStream) return;\n\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const analyser = audioContext.createAnalyser();\n      const source = audioContext.createMediaStreamSource(audioStream);\n\n      analyser.fftSize = 256;\n      source.connect(analyser);\n\n      audioContextRef.current = audioContext;\n      analyserRef.current = analyser;\n\n      // Start monitoring audio levels\n      const monitorAudio = () => {\n        if (!analyserRef.current) return;\n\n        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\n        analyserRef.current.getByteFrequencyData(dataArray);\n\n        // Calculate average volume\n        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;\n        const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1\n\n        setAudioLevels(prev => ({\n          ...prev,\n          microphone: normalizedLevel\n        }));\n\n        if (isRecording) {\n          requestAnimationFrame(monitorAudio);\n        }\n      };\n\n      monitorAudio();\n    } catch (error) {\n      console.warn('Audio monitoring setup failed:', error);\n    }\n  };\n\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  \n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  \n  return (\n    <div className=\"app\">\n      <header className=\"header\">\n        <h1>Screen Recorder</h1>\n        <div className=\"header-controls\">\n          <button\n            className=\"btn btn-secondary\"\n            onClick={() => setShowSettings(!showSettings)}\n            disabled={isRecording}\n          >\n            ⚙️ Settings\n          </button>\n          {permissions.screen === 'denied' && (\n            <button className=\"btn btn-secondary\" onClick={requestPermissions}>\n              Grant Permissions\n            </button>\n          )}\n        </div>\n      </header>\n\n      {showSettings && (\n        <div className=\"settings-panel\">\n          <h3>Recording Settings</h3>\n\n          <div className=\"settings-group\">\n            <h4>Audio</h4>\n            <label className=\"setting-item\">\n              <input\n                type=\"checkbox\"\n                checked={recordingSettings.includeMicrophone}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  includeMicrophone: e.target.checked\n                }))}\n              />\n              Include Microphone\n            </label>\n            <label className=\"setting-item\">\n              <input\n                type=\"checkbox\"\n                checked={recordingSettings.includeSystemAudio}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  includeSystemAudio: e.target.checked\n                }))}\n              />\n              Include System Audio\n            </label>\n          </div>\n\n          <div className=\"settings-group\">\n            <h4>Video</h4>\n            <label className=\"setting-item\">\n              <input\n                type=\"checkbox\"\n                checked={recordingSettings.includeWebcam}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  includeWebcam: e.target.checked\n                }))}\n              />\n              Include Webcam\n            </label>\n\n            <div className=\"setting-item\">\n              <label>Resolution:</label>\n              <select\n                value={recordingSettings.resolution}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  resolution: e.target.value\n                }))}\n              >\n                <option value=\"1280x720\">720p (1280x720)</option>\n                <option value=\"1920x1080\">1080p (1920x1080)</option>\n                <option value=\"2560x1440\">1440p (2560x1440)</option>\n                <option value=\"3840x2160\">4K (3840x2160)</option>\n              </select>\n            </div>\n\n            <div className=\"setting-item\">\n              <label>Frame Rate:</label>\n              <select\n                value={recordingSettings.frameRate}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  frameRate: parseInt(e.target.value)\n                }))}\n              >\n                <option value={30}>30 FPS</option>\n                <option value={60}>60 FPS</option>\n              </select>\n            </div>\n\n            <div className=\"setting-item\">\n              <label>Quality:</label>\n              <select\n                value={recordingSettings.quality}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  quality: e.target.value\n                }))}\n              >\n                <option value=\"draft\">Draft (Smaller file)</option>\n                <option value=\"standard\">Standard</option>\n                <option value=\"high\">High Quality</option>\n              </select>\n            </div>\n          </div>\n\n          <div className=\"settings-group\">\n            <h4>Recording</h4>\n            <div className=\"setting-item\">\n              <label>Countdown Timer (seconds):</label>\n              <select\n                value={countdown}\n                onChange={(e) => setCountdown(parseInt(e.target.value))}\n              >\n                <option value={0}>No countdown</option>\n                <option value={3}>3 seconds</option>\n                <option value={5}>5 seconds</option>\n                <option value={10}>10 seconds</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <main className=\"content\">\n        <div className=\"source-selector\">\n          <h3>Select Screen or Window:</h3>\n          <div className=\"sources-grid\">\n            {sources.map((source) => (\n              <div\n                key={source.id}\n                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}\n                onClick={() => setSelectedSource(source)}\n              >\n                <img\n                  src={source.thumbnail}\n                  alt={source.name}\n                  className=\"source-thumbnail\"\n                />\n                <div className=\"source-name\">{source.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"recording-area\">\n          {recordedBlob ? (\n            <div className=\"preview-container\">\n              <video\n                ref={videoRef}\n                className=\"preview-video\"\n                controls\n                autoPlay={false}\n                preload=\"metadata\"\n                muted\n                playsInline\n              />\n              <div className=\"recording-info\">\n                <p>Recording completed • Duration: {formatDuration(recordingDuration)}</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"status-display\">\n              <div className={`status-box ${isRecording ? 'recording' : ''}`}>\n                {isRecording ? (\n                  <div className=\"recording-status\">\n                    <div className=\"recording-indicator\">\n                      <span className=\"recording-dot\"></span>\n                      RECORDING\n                    </div>\n                    <div className=\"recording-duration\">\n                      {formatDuration(recordingDuration)}\n                    </div>\n                    <div className=\"recording-source\">\n                      {selectedSource?.name}\n                    </div>\n                    <div className=\"recording-settings-info\">\n                      {recordingSettings.resolution} • {recordingSettings.frameRate}fps • {recordingSettings.quality}\n                    </div>\n\n                    {/* Audio level indicators */}\n                    {(recordingSettings.includeMicrophone || recordingSettings.includeSystemAudio) && (\n                      <div className=\"audio-levels\">\n                        {recordingSettings.includeMicrophone && (\n                          <div className=\"audio-level-item\">\n                            <span className=\"audio-label\">🎤</span>\n                            <div className=\"audio-meter\">\n                              <div\n                                className=\"audio-level-bar\"\n                                style={{ width: `${audioLevels.microphone * 100}%` }}\n                              />\n                            </div>\n                          </div>\n                        )}\n                        {recordingSettings.includeSystemAudio && (\n                          <div className=\"audio-level-item\">\n                            <span className=\"audio-label\">🔊</span>\n                            <div className=\"audio-meter\">\n                              <div\n                                className=\"audio-level-bar\"\n                                style={{ width: `${audioLevels.system * 100}%` }}\n                              />\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"ready-status\">\n                    <div className=\"status-text\">{status}</div>\n                    {selectedSource && (\n                      <div className=\"selected-source\">Ready to record: {selectedSource.name}</div>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              {/* Webcam preview during recording */}\n              {webcamStream && isRecording && (\n                <div className=\"webcam-preview\">\n                  <video\n                    ref={webcamRef}\n                    className=\"webcam-video\"\n                    autoPlay\n                    muted\n                    playsInline\n                  />\n                  <div className=\"webcam-label\">Webcam</div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n        \n        <div className=\"controls\">\n          {!isRecording && !recordedBlob && (\n            <button\n              className=\"btn btn-primary\"\n              onClick={startRecording}\n              disabled={!selectedSource || permissions.screen === 'denied'}\n            >\n              Start Recording\n            </button>\n          )}\n          \n          {isRecording && (\n            <button className=\"btn btn-danger\" onClick={stopRecording}>\n              Stop Recording\n            </button>\n          )}\n          \n          {recordedBlob && !isRecording && (\n            <>\n              {!showExportOptions ? (\n                <button \n                  className=\"btn btn-primary\" \n                  onClick={() => setShowExportOptions(true)}\n                >\n                  Download Recording\n                </button>\n              ) : (\n                <div className=\"export-options\">\n                  <h4>Choose format:</h4>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('webm')}\n                  >\n                    Download WebM (Fast)\n                  </button>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('mp4')}\n                  >\n                    Download MP4 (Compatible)\n                  </button>\n                  <button \n                    className=\"btn btn-secondary\" \n                    onClick={() => setShowExportOptions(false)}\n                  >\n                    Cancel\n                  </button>\n                </div>\n              )}\n              <button className=\"btn btn-secondary\" onClick={clearRecording}>\n                New Recording\n              </button>\n            </>\n          )}\n          \n          <button className=\"btn btn-secondary\" onClick={loadSources}>\n            Refresh Sources\n          </button>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,iBAAiB,CAAC;EACvD,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC;IACzD2B,kBAAkB,EAAE,KAAK;IACzBC,iBAAiB,EAAE,IAAI;IACvBC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,WAAW;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,UAAU,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAE5E,MAAMC,QAAQ,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM2C,SAAS,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4C,gBAAgB,GAAG5C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM6C,SAAS,GAAG7C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM8C,mBAAmB,GAAG9C,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM+C,eAAe,GAAG/C,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMgD,WAAW,GAAGhD,MAAM,CAAC,IAAI,CAAC;EAEhCC,SAAS,CAAC,MAAM;IACdgD,gBAAgB,CAAC,CAAC;IAClBC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAME,KAAK,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACJ,gBAAgB,CAAC,CAAC;IACzDhC,cAAc,CAACkC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,OAAO,GAAG,MAAMH,MAAM,CAACC,WAAW,CAACC,kBAAkB,CAAC,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACX,MAAMN,gBAAgB,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMxC,OAAO,GAAG,MAAM0C,MAAM,CAACC,WAAW,CAACG,UAAU,CAAC,CAAC;IACrD7C,UAAU,CAACD,OAAO,CAAC;IACnB,IAAIA,OAAO,CAAC+C,MAAM,GAAG,CAAC,IAAI,CAAC7C,cAAc,EAAE;MACzC;MACA,MAAM8C,MAAM,GAAGhD,OAAO,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;MACxFjD,iBAAiB,CAAC6C,MAAM,IAAIhD,OAAO,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMqD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACnD,cAAc,EAAE;MACnBO,SAAS,CAAC,4CAA4C,CAAC;MACvD;IACF;IAEA,IAAI;MACF;MACA,IAAIe,SAAS,GAAG,CAAC,EAAE;QACjB,MAAM8B,cAAc,CAAC,CAAC;MACxB;MAEA7C,SAAS,CAAC,uBAAuB,CAAC;MAClCE,oBAAoB,CAAC,CAAC,CAAC;;MAEvB;MACAyB,mBAAmB,CAACmB,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC9C7C,oBAAoB,CAAC8C,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG7C,iBAAiB,CAACK,UAAU,CAACyC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;;MAE3E;MACA,MAAMC,iBAAiB,GAAG;QACxBC,KAAK,EAAElD,iBAAiB,CAACE,kBAAkB,GAAG;UAC5CiD,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAEjE,cAAc,CAACkE;UACtC;QACF,CAAC,GAAG,KAAK;QACTC,KAAK,EAAE;UACLJ,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAEjE,cAAc,CAACkE,EAAE;YACtCE,QAAQ,EAAEZ,KAAK;YACfa,QAAQ,EAAEb,KAAK;YACfc,SAAS,EAAEb,MAAM;YACjBc,SAAS,EAAEd,MAAM;YACjBe,YAAY,EAAE5D,iBAAiB,CAACM,SAAS;YACzCuD,YAAY,EAAE7D,iBAAiB,CAACM;UAClC;QACF;MACF,CAAC;MAED,MAAMwD,YAAY,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAChB,iBAAiB,CAAC;;MAEjF;MACA,IAAIiB,gBAAgB,GAAG,IAAI;MAC3B,IAAIlE,iBAAiB,CAACG,iBAAiB,EAAE;QACvC,IAAI;UACF+D,gBAAgB,GAAG,MAAMH,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;YAC3Df,KAAK,EAAE;cACLiB,gBAAgB,EAAE,IAAI;cACtBC,gBAAgB,EAAE,IAAI;cACtBC,eAAe,EAAE,IAAI;cACrBC,UAAU,EAAE;YACd,CAAC;YACDf,KAAK,EAAE;UACT,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOgB,UAAU,EAAE;UACnBC,OAAO,CAACC,IAAI,CAAC,wDAAwD,CAAC;QACxE;MACF;;MAEA;MACA,IAAIC,iBAAiB,GAAG,IAAI;MAC5B,IAAI1E,iBAAiB,CAACI,aAAa,EAAE;QACnC,IAAI;UACFsE,iBAAiB,GAAG,MAAMX,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;YAC5DV,KAAK,EAAE;cACLX,KAAK,EAAE;gBAAE+B,KAAK,EAAE;cAAI,CAAC;cACrB9B,MAAM,EAAE;gBAAE8B,KAAK,EAAE;cAAI,CAAC;cACtBrE,SAAS,EAAE;gBAAEqE,KAAK,EAAE;cAAG;YACzB,CAAC;YACDzB,KAAK,EAAE;UACT,CAAC,CAAC;UACFzC,eAAe,CAACiE,iBAAiB,CAAC;;UAElC;UACA,IAAIvD,SAAS,CAACsB,OAAO,EAAE;YACrBtB,SAAS,CAACsB,OAAO,CAACmC,SAAS,GAAGF,iBAAiB;UACjD;QACF,CAAC,CAAC,OAAOG,WAAW,EAAE;UACpBL,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;QAChE;MACF;;MAEA;MACA,IAAIK,cAAc,GAAGhB,YAAY;;MAEjC;MACA,IAAII,gBAAgB,EAAE;QACpB,MAAMa,WAAW,GAAGb,gBAAgB,CAACc,cAAc,CAAC,CAAC;QACrDD,WAAW,CAACE,OAAO,CAACC,KAAK,IAAIJ,cAAc,CAACK,QAAQ,CAACD,KAAK,CAAC,CAAC;;QAE5D;QACAE,oBAAoB,CAAClB,gBAAgB,CAAC;MACxC;;MAEA;MACA;;MAEA7C,SAAS,CAACoB,OAAO,GAAGqC,cAAc;;MAElC;MACA,MAAMO,QAAQ,GAAGC,kBAAkB,CAAC,CAAC;MACrC,MAAMC,aAAa,GAAG,IAAIC,aAAa,CAACV,cAAc,EAAE;QACtDO,QAAQ;QACRI,kBAAkB,EAAEC,eAAe,CAAC1F,iBAAiB,CAACO,OAAO,EAAEqC,KAAK,EAAEC,MAAM;MAC9E,CAAC,CAAC;MAEF,MAAM8C,MAAM,GAAG,EAAE;MAEjBJ,aAAa,CAACK,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBJ,MAAM,CAACK,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzB;MACF,CAAC;MAEDP,aAAa,CAACU,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACR,MAAM,EAAE;UAAES,IAAI,EAAEf;QAAS,CAAC,CAAC;QACjD9F,eAAe,CAAC2G,IAAI,CAAC;QACrBvG,SAAS,CAAC,2CAA2C,CAAC;;QAEtD;QACA,IAAI+E,iBAAiB,EAAE;UACrBA,iBAAiB,CAAC2B,SAAS,CAAC,CAAC,CAACpB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACoB,IAAI,CAAC,CAAC,CAAC;UAC5D7F,eAAe,CAAC,IAAI,CAAC;QACvB;;QAEA;QACA8F,UAAU,CAAC,MAAM;UACf,IAAIrF,QAAQ,CAACuB,OAAO,IAAIyD,IAAI,EAAE;YAC5B,MAAMM,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;YAC1ChF,QAAQ,CAACuB,OAAO,CAACkE,GAAG,GAAGH,QAAQ;YAC/BtF,QAAQ,CAACuB,OAAO,CAACmE,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3B;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MAEDxF,gBAAgB,CAACqB,OAAO,GAAG8C,aAAa;MACxCA,aAAa,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE1B5H,cAAc,CAAC,IAAI,CAAC;MACpBU,SAAS,CAAC,uBAAuB,CAAC;IAEpC,CAAC,CAAC,OAAOmH,KAAK,EAAE;MACdtC,OAAO,CAACsC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDnH,SAAS,CAAC,sDAAsD,CAAC;MACjE,IAAI2B,mBAAmB,CAACmB,OAAO,EAAE;QAC/BsE,aAAa,CAACzF,mBAAmB,CAACmB,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMuE,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI5F,gBAAgB,CAACqB,OAAO,IAAIzD,WAAW,EAAE;MAC3CoC,gBAAgB,CAACqB,OAAO,CAAC6D,IAAI,CAAC,CAAC;;MAE/B;MACA,IAAIhF,mBAAmB,CAACmB,OAAO,EAAE;QAC/BsE,aAAa,CAACzF,mBAAmB,CAACmB,OAAO,CAAC;MAC5C;;MAEA;MACA,IAAIpB,SAAS,CAACoB,OAAO,EAAE;QACrBpB,SAAS,CAACoB,OAAO,CAAC4D,SAAS,CAAC,CAAC,CAACpB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACoB,IAAI,CAAC,CAAC,CAAC;MAC9D;;MAEA;MACA,IAAI9F,YAAY,EAAE;QAChBA,YAAY,CAAC6F,SAAS,CAAC,CAAC,CAACpB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACoB,IAAI,CAAC,CAAC,CAAC;QACvD7F,eAAe,CAAC,IAAI,CAAC;MACvB;;MAEA;MACA,IAAIc,eAAe,CAACkB,OAAO,EAAE;QAC3BlB,eAAe,CAACkB,OAAO,CAACwE,KAAK,CAAC,CAAC;QAC/B1F,eAAe,CAACkB,OAAO,GAAG,IAAI;QAC9BjB,WAAW,CAACiB,OAAO,GAAG,IAAI;MAC5B;;MAEA;MACA1B,cAAc,CAAC;QAAEC,UAAU,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,CAAC;MAE5ChC,cAAc,CAAC,KAAK,CAAC;MACrBU,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC;EAED,MAAMuH,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAMjF,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIkF,OAAO,CAAEC,OAAO,IAAK;MAC9B,IAAIC,KAAK,GAAGlH,SAAS;MACrBf,SAAS,CAAC,uBAAuBiI,KAAK,KAAK,CAAC;MAE5C,MAAMC,iBAAiB,GAAGnF,WAAW,CAAC,MAAM;QAC1CkF,KAAK,EAAE;QACP,IAAIA,KAAK,GAAG,CAAC,EAAE;UACbjI,SAAS,CAAC,uBAAuBiI,KAAK,KAAK,CAAC;QAC9C,CAAC,MAAM;UACLjI,SAAS,CAAC,uBAAuB,CAAC;UAClCoH,aAAa,CAACc,iBAAiB,CAAC;UAChCF,OAAO,CAAC,CAAC;QACX;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMrC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMwC,KAAK,GAAG,CACZ,4BAA4B,EAC5B,4BAA4B,EAC5B,6BAA6B,EAC7B,YAAY,CACb;IAED,KAAK,MAAM1B,IAAI,IAAI0B,KAAK,EAAE;MACxB,IAAItC,aAAa,CAACuC,eAAe,CAAC3B,IAAI,CAAC,EAAE;QACvC,OAAOA,IAAI;MACb;IACF;IACA,OAAO,YAAY;EACrB,CAAC;EAED,MAAMV,eAAe,GAAGA,CAACnF,OAAO,EAAEqC,KAAK,EAAEC,MAAM,KAAK;IAClD,MAAMmF,UAAU,GAAGpF,KAAK,GAAGC,MAAM;IACjC,MAAMoF,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC,CAAC;;IAEpC,QAAQzH,OAAO;MACb,KAAK,OAAO;QACV,OAAO8G,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI;MAAE;MAC5C,KAAK,UAAU;QACb,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;MAAE;MAC1C,KAAK,MAAM;QACT,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;MAAE;MAC1C;QACE,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;IAC1C;EACF,CAAC;EAED,MAAM7C,oBAAoB,GAAI8C,WAAW,IAAK;IAC5C,IAAI,CAACA,WAAW,EAAE;IAElB,IAAI;MACF,MAAMC,YAAY,GAAG,KAAKvG,MAAM,CAACwG,YAAY,IAAIxG,MAAM,CAACyG,kBAAkB,EAAE,CAAC;MAC7E,MAAMC,QAAQ,GAAGH,YAAY,CAACI,cAAc,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAGL,YAAY,CAACM,uBAAuB,CAACP,WAAW,CAAC;MAEhEI,QAAQ,CAACI,OAAO,GAAG,GAAG;MACtBF,MAAM,CAACG,OAAO,CAACL,QAAQ,CAAC;MAExB/G,eAAe,CAACkB,OAAO,GAAG0F,YAAY;MACtC3G,WAAW,CAACiB,OAAO,GAAG6F,QAAQ;;MAE9B;MACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAI,CAACpH,WAAW,CAACiB,OAAO,EAAE;QAE1B,MAAMoG,SAAS,GAAG,IAAIC,UAAU,CAACtH,WAAW,CAACiB,OAAO,CAACsG,iBAAiB,CAAC;QACvEvH,WAAW,CAACiB,OAAO,CAACuG,oBAAoB,CAACH,SAAS,CAAC;;QAEnD;QACA,MAAMI,OAAO,GAAGJ,SAAS,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGP,SAAS,CAAC5G,MAAM;QACnF,MAAMoH,eAAe,GAAGhC,IAAI,CAACiC,GAAG,CAACL,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEpDlI,cAAc,CAAC4B,IAAI,KAAK;UACtB,GAAGA,IAAI;UACP3B,UAAU,EAAEqI;QACd,CAAC,CAAC,CAAC;QAEH,IAAIrK,WAAW,EAAE;UACfuK,qBAAqB,CAACX,YAAY,CAAC;QACrC;MACF,CAAC;MAEDA,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdtC,OAAO,CAACC,IAAI,CAAC,gCAAgC,EAAEqC,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAM0C,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,GAAG,MAAM,KAAK;IACnD,IAAInK,YAAY,EAAE;MAChB,IAAI;QACF,IAAImK,MAAM,KAAK,MAAM,EAAE;UACrB;UACA,MAAMC,GAAG,GAAGjD,GAAG,CAACC,eAAe,CAACpH,YAAY,CAAC;UAC7C,MAAMqK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGJ,GAAG;UACZC,CAAC,CAACI,QAAQ,GAAG,aAAa,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UACzFR,CAAC,CAACS,KAAK,CAAC,CAAC;UACT3D,GAAG,CAAC4D,eAAe,CAACX,GAAG,CAAC;UACxB3J,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,MAAM,IAAI0J,MAAM,KAAK,KAAK,EAAE;UAC3B9J,SAAS,CAAC,sBAAsB,CAAC;;UAEjC;UACA,MAAM2K,WAAW,GAAG,MAAMhL,YAAY,CAACgL,WAAW,CAAC,CAAC;UACpD,MAAMC,UAAU,GAAG,IAAIzB,UAAU,CAACwB,WAAW,CAAC;;UAE9C;UACA,MAAME,QAAQ,GAAG,aAAa,IAAIR,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UAC7F,MAAMM,SAAS,GAAG,MAAM7I,MAAM,CAACC,WAAW,CAAC6I,aAAa,CAACH,UAAU,EAAEC,QAAQ,CAAC;UAE9E,IAAIC,SAAS,EAAE;YACb9K,SAAS,CAAC,yBAAyB,CAAC;YACpCI,oBAAoB,CAAC,KAAK,CAAC;UAC7B;QACF;MACF,CAAC,CAAC,OAAO+G,KAAK,EAAE;QACdtC,OAAO,CAACsC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDnH,SAAS,CAAC,0BAA0B,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMgL,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAIzJ,QAAQ,CAACuB,OAAO,IAAIvB,QAAQ,CAACuB,OAAO,CAACkE,GAAG,EAAE;MAC5CF,GAAG,CAAC4D,eAAe,CAACnJ,QAAQ,CAACuB,OAAO,CAACkE,GAAG,CAAC;MACzCzF,QAAQ,CAACuB,OAAO,CAACkE,GAAG,GAAG,EAAE;IAC3B;IACApH,eAAe,CAAC,IAAI,CAAC;IACrBI,SAAS,CAAC,iBAAiB,CAAC;EAC9B,CAAC;EAED,oBACEhB,OAAA;IAAKiM,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBlM,OAAA;MAAQiM,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACxBlM,OAAA;QAAAkM,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBtM,OAAA;QAAKiM,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlM,OAAA;UACEiM,SAAS,EAAC,mBAAmB;UAC7BM,OAAO,EAAEA,CAAA,KAAMrK,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9CuK,QAAQ,EAAEnM,WAAY;UAAA6L,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRzL,WAAW,CAAC0C,MAAM,KAAK,QAAQ,iBAC9BvD,OAAA;UAAQiM,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAEpJ,kBAAmB;UAAA+I,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAERrK,YAAY,iBACXjC,OAAA;MAAKiM,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlM,OAAA;QAAAkM,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE3BtM,OAAA;QAAKiM,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlM,OAAA;UAAAkM,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdtM,OAAA;UAAOiM,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BlM,OAAA;YACEyH,IAAI,EAAC,UAAU;YACfgF,OAAO,EAAEpL,iBAAiB,CAACG,iBAAkB;YAC7CkL,QAAQ,EAAGC,CAAC,IAAKrL,oBAAoB,CAAC0C,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPxC,iBAAiB,EAAEmL,CAAC,CAACC,MAAM,CAACH;YAC9B,CAAC,CAAC;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,sBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtM,OAAA;UAAOiM,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BlM,OAAA;YACEyH,IAAI,EAAC,UAAU;YACfgF,OAAO,EAAEpL,iBAAiB,CAACE,kBAAmB;YAC9CmL,QAAQ,EAAGC,CAAC,IAAKrL,oBAAoB,CAAC0C,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPzC,kBAAkB,EAAEoL,CAAC,CAACC,MAAM,CAACH;YAC/B,CAAC,CAAC;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,wBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtM,OAAA;QAAKiM,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlM,OAAA;UAAAkM,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdtM,OAAA;UAAOiM,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BlM,OAAA;YACEyH,IAAI,EAAC,UAAU;YACfgF,OAAO,EAAEpL,iBAAiB,CAACI,aAAc;YACzCiL,QAAQ,EAAGC,CAAC,IAAKrL,oBAAoB,CAAC0C,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPvC,aAAa,EAAEkL,CAAC,CAACC,MAAM,CAACH;YAC1B,CAAC,CAAC;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,kBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERtM,OAAA;UAAKiM,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlM,OAAA;YAAAkM,QAAA,EAAO;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1BtM,OAAA;YACEyK,KAAK,EAAEpJ,iBAAiB,CAACK,UAAW;YACpCgL,QAAQ,EAAGC,CAAC,IAAKrL,oBAAoB,CAAC0C,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPtC,UAAU,EAAEiL,CAAC,CAACC,MAAM,CAACnC;YACvB,CAAC,CAAC,CAAE;YAAAyB,QAAA,gBAEJlM,OAAA;cAAQyK,KAAK,EAAC,UAAU;cAAAyB,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjDtM,OAAA;cAAQyK,KAAK,EAAC,WAAW;cAAAyB,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpDtM,OAAA;cAAQyK,KAAK,EAAC,WAAW;cAAAyB,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpDtM,OAAA;cAAQyK,KAAK,EAAC,WAAW;cAAAyB,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtM,OAAA;UAAKiM,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlM,OAAA;YAAAkM,QAAA,EAAO;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1BtM,OAAA;YACEyK,KAAK,EAAEpJ,iBAAiB,CAACM,SAAU;YACnC+K,QAAQ,EAAGC,CAAC,IAAKrL,oBAAoB,CAAC0C,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPrC,SAAS,EAAEkL,QAAQ,CAACF,CAAC,CAACC,MAAM,CAACnC,KAAK;YACpC,CAAC,CAAC,CAAE;YAAAyB,QAAA,gBAEJlM,OAAA;cAAQyK,KAAK,EAAE,EAAG;cAAAyB,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCtM,OAAA;cAAQyK,KAAK,EAAE,EAAG;cAAAyB,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtM,OAAA;UAAKiM,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlM,OAAA;YAAAkM,QAAA,EAAO;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvBtM,OAAA;YACEyK,KAAK,EAAEpJ,iBAAiB,CAACO,OAAQ;YACjC8K,QAAQ,EAAGC,CAAC,IAAKrL,oBAAoB,CAAC0C,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPpC,OAAO,EAAE+K,CAAC,CAACC,MAAM,CAACnC;YACpB,CAAC,CAAC,CAAE;YAAAyB,QAAA,gBAEJlM,OAAA;cAAQyK,KAAK,EAAC,OAAO;cAAAyB,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnDtM,OAAA;cAAQyK,KAAK,EAAC,UAAU;cAAAyB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1CtM,OAAA;cAAQyK,KAAK,EAAC,MAAM;cAAAyB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtM,OAAA;QAAKiM,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlM,OAAA;UAAAkM,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClBtM,OAAA;UAAKiM,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlM,OAAA;YAAAkM,QAAA,EAAO;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzCtM,OAAA;YACEyK,KAAK,EAAE1I,SAAU;YACjB2K,QAAQ,EAAGC,CAAC,IAAK3K,YAAY,CAAC6K,QAAQ,CAACF,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAC,CAAE;YAAAyB,QAAA,gBAExDlM,OAAA;cAAQyK,KAAK,EAAE,CAAE;cAAAyB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvCtM,OAAA;cAAQyK,KAAK,EAAE,CAAE;cAAAyB,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCtM,OAAA;cAAQyK,KAAK,EAAE,CAAE;cAAAyB,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCtM,OAAA;cAAQyK,KAAK,EAAE,EAAG;cAAAyB,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDtM,OAAA;MAAMiM,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACvBlM,OAAA;QAAKiM,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlM,OAAA;UAAAkM,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCtM,OAAA;UAAKiM,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B3L,OAAO,CAAC6D,GAAG,CAAEyF,MAAM,iBAClB7J,OAAA;YAEEiM,SAAS,EAAE,eAAe,CAAAxL,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEkE,EAAE,MAAKkF,MAAM,CAAClF,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/E4H,OAAO,EAAEA,CAAA,KAAM7L,iBAAiB,CAACmJ,MAAM,CAAE;YAAAqC,QAAA,gBAEzClM,OAAA;cACEgI,GAAG,EAAE6B,MAAM,CAACiD,SAAU;cACtBC,GAAG,EAAElD,MAAM,CAACnG,IAAK;cACjBuI,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACFtM,OAAA;cAAKiM,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAErC,MAAM,CAACnG;YAAI;cAAAyI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAT3CzC,MAAM,CAAClF,EAAE;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtM,OAAA;QAAKiM,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BvL,YAAY,gBACXX,OAAA;UAAKiM,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClM,OAAA;YACEgN,GAAG,EAAEzK,QAAS;YACd0J,SAAS,EAAC,eAAe;YACzBgB,QAAQ;YACRC,QAAQ,EAAE,KAAM;YAChBC,OAAO,EAAC,UAAU;YAClBC,KAAK;YACLC,WAAW;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACFtM,OAAA;YAAKiM,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BlM,OAAA;cAAAkM,QAAA,GAAG,uCAAgC,EAAC3D,cAAc,CAACtH,iBAAiB,CAAC;YAAA;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENtM,OAAA;UAAKiM,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlM,OAAA;YAAKiM,SAAS,EAAE,cAAc5L,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;YAAA6L,QAAA,EAC5D7L,WAAW,gBACVL,OAAA;cAAKiM,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BlM,OAAA;gBAAKiM,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClClM,OAAA;kBAAMiM,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,aAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtM,OAAA;gBAAKiM,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAChC3D,cAAc,CAACtH,iBAAiB;cAAC;gBAAAkL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNtM,OAAA;gBAAKiM,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BzL,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiD;cAAI;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNtM,OAAA;gBAAKiM,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GACrC7K,iBAAiB,CAACK,UAAU,EAAC,UAAG,EAACL,iBAAiB,CAACM,SAAS,EAAC,aAAM,EAACN,iBAAiB,CAACO,OAAO;cAAA;gBAAAuK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC,EAGL,CAACjL,iBAAiB,CAACG,iBAAiB,IAAIH,iBAAiB,CAACE,kBAAkB,kBAC3EvB,OAAA;gBAAKiM,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC1B7K,iBAAiB,CAACG,iBAAiB,iBAClCxB,OAAA;kBAAKiM,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BlM,OAAA;oBAAMiM,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvCtM,OAAA;oBAAKiM,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1BlM,OAAA;sBACEiM,SAAS,EAAC,iBAAiB;sBAC3BqB,KAAK,EAAE;wBAAErJ,KAAK,EAAE,GAAG9B,WAAW,CAACE,UAAU,GAAG,GAAG;sBAAI;oBAAE;sBAAA8J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EACAjL,iBAAiB,CAACE,kBAAkB,iBACnCvB,OAAA;kBAAKiM,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BlM,OAAA;oBAAMiM,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvCtM,OAAA;oBAAKiM,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1BlM,OAAA;sBACEiM,SAAS,EAAC,iBAAiB;sBAC3BqB,KAAK,EAAE;wBAAErJ,KAAK,EAAE,GAAG9B,WAAW,CAACG,MAAM,GAAG,GAAG;sBAAI;oBAAE;sBAAA6J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAENtM,OAAA;cAAKiM,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlM,OAAA;gBAAKiM,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEnL;cAAM;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1C7L,cAAc,iBACbT,OAAA;gBAAKiM,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,mBAAiB,EAACzL,cAAc,CAACiD,IAAI;cAAA;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLzK,YAAY,IAAIxB,WAAW,iBAC1BL,OAAA;YAAKiM,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BlM,OAAA;cACEgN,GAAG,EAAExK,SAAU;cACfyJ,SAAS,EAAC,cAAc;cACxBiB,QAAQ;cACRE,KAAK;cACLC,WAAW;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACFtM,OAAA;cAAKiM,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtM,OAAA;QAAKiM,SAAS,EAAC,UAAU;QAAAC,QAAA,GACtB,CAAC7L,WAAW,IAAI,CAACM,YAAY,iBAC5BX,OAAA;UACEiM,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAE3I,cAAe;UACxB4I,QAAQ,EAAE,CAAC/L,cAAc,IAAII,WAAW,CAAC0C,MAAM,KAAK,QAAS;UAAA2I,QAAA,EAC9D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAjM,WAAW,iBACVL,OAAA;UAAQiM,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAElE,aAAc;UAAA6D,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEA3L,YAAY,IAAI,CAACN,WAAW,iBAC3BL,OAAA,CAAAE,SAAA;UAAAgM,QAAA,GACG,CAAC/K,iBAAiB,gBACjBnB,OAAA;YACEiM,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAMnL,oBAAoB,CAAC,IAAI,CAAE;YAAA8K,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAETtM,OAAA;YAAKiM,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BlM,OAAA;cAAAkM,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBtM,OAAA;cACEiM,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAM1B,iBAAiB,CAAC,MAAM,CAAE;cAAAqB,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtM,OAAA;cACEiM,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAM1B,iBAAiB,CAAC,KAAK,CAAE;cAAAqB,QAAA,EACzC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtM,OAAA;cACEiM,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAEA,CAAA,KAAMnL,oBAAoB,CAAC,KAAK,CAAE;cAAA8K,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDtM,OAAA;YAAQiM,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAEP,cAAe;YAAAE,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,eAEDtM,OAAA;UAAQiM,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAExJ,WAAY;UAAAmJ,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAClM,EAAA,CAtrBQD,GAAG;AAAAoN,EAAA,GAAHpN,GAAG;AAwrBZ,eAAeA,GAAG;AAAC,IAAAoN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}