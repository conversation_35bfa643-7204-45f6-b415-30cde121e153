{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js\",\n  _s = $RefreshSig$();\n/// <reference path=\"./electron.d.ts\" />\nimport { useState, useRef, useEffect } from 'react';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n\n  // New Phase 2 state variables\n  const [recordingSettings, setRecordingSettings] = useState({\n    includeSystemAudio: false,\n    includeMicrophone: true,\n    includeWebcam: false,\n    resolution: '1920x1080',\n    frameRate: 30,\n    quality: 'high',\n    selectedMicrophone: 'default',\n    selectedCamera: 'default',\n    selectedSpeaker: 'default'\n  });\n  const [webcamStream, setWebcamStream] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [audioLevels, setAudioLevels] = useState({\n    microphone: 0,\n    system: 0\n  });\n  const [tempSettings, setTempSettings] = useState(null);\n  const [availableDevices, setAvailableDevices] = useState({\n    microphones: [],\n    cameras: [],\n    speakers: []\n  });\n\n  // Phase 3: Smart Zoom & Cursor Tracking state\n  const [cursorTracking, setCursorTracking] = useState({\n    enabled: true,\n    positions: [],\n    clicks: [],\n    currentZoom: {\n      x: 0,\n      y: 0,\n      scale: 1\n    },\n    isZooming: false\n  });\n  const videoRef = useRef(null);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  const cursorTrackingIntervalRef = useRef(null);\n  const zoomCanvasRef = useRef(null);\n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n    loadAvailableDevices();\n  }, []);\n  const loadAvailableDevices = async () => {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const microphones = devices.filter(device => device.kind === 'audioinput').map(device => ({\n        id: device.deviceId,\n        label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,\n        groupId: device.groupId\n      }));\n      const cameras = devices.filter(device => device.kind === 'videoinput').map(device => ({\n        id: device.deviceId,\n        label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,\n        groupId: device.groupId\n      }));\n      const speakers = devices.filter(device => device.kind === 'audiooutput').map(device => ({\n        id: device.deviceId,\n        label: device.label || `Speaker ${device.deviceId.slice(0, 8)}`,\n        groupId: device.groupId\n      }));\n      setAvailableDevices({\n        microphones: [{\n          id: 'default',\n          label: 'Default Microphone',\n          groupId: null\n        }, ...microphones],\n        cameras: [{\n          id: 'default',\n          label: 'Default Camera',\n          groupId: null\n        }, ...cameras],\n        speakers: [{\n          id: 'default',\n          label: 'Default Speaker',\n          groupId: null\n        }, ...speakers]\n      });\n    } catch (error) {\n      console.warn('Failed to enumerate devices:', error);\n      // Set default options if enumeration fails\n      setAvailableDevices({\n        microphones: [{\n          id: 'default',\n          label: 'Default Microphone',\n          groupId: null\n        }],\n        cameras: [{\n          id: 'default',\n          label: 'Default Camera',\n          groupId: null\n        }],\n        speakers: [{\n          id: 'default',\n          label: 'Default Speaker',\n          groupId: null\n        }]\n      });\n    }\n  };\n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n\n    // Show status message about permissions\n    const permissionStatus = [];\n    if (perms.screen === 'granted') permissionStatus.push('✅ Screen recording');else permissionStatus.push('❌ Screen recording');\n    if (perms.microphone === 'granted') permissionStatus.push('✅ Microphone');else permissionStatus.push('❌ Microphone');\n    if (perms.camera === 'granted') permissionStatus.push('✅ Camera');else permissionStatus.push('❌ Camera');\n    setStatus(`Permissions: ${permissionStatus.join(', ')}`);\n\n    // Reset status after 3 seconds\n    setTimeout(() => {\n      if (!isRecording && !recordedBlob) {\n        setStatus('Ready to record');\n      }\n    }, 3000);\n  };\n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    try {\n      // Start countdown if enabled\n      if (countdown > 0) {\n        await startCountdown();\n      }\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Parse resolution settings\n      const [width, height] = recordingSettings.resolution.split('x').map(Number);\n\n      // Get screen stream (without system audio for now - will implement properly in Phase 3)\n      const screenConstraints = {\n        audio: false,\n        // System audio capture needs more complex implementation\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: width,\n            maxWidth: width,\n            minHeight: height,\n            maxHeight: height,\n            minFrameRate: recordingSettings.frameRate,\n            maxFrameRate: recordingSettings.frameRate\n          }\n        }\n      };\n      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);\n\n      // Get microphone stream if enabled\n      let microphoneStream = null;\n      if (recordingSettings.includeMicrophone) {\n        try {\n          const audioConstraints = {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 48000\n          };\n\n          // Add device ID if not default\n          if (recordingSettings.selectedMicrophone !== 'default') {\n            audioConstraints.deviceId = {\n              exact: recordingSettings.selectedMicrophone\n            };\n          }\n          microphoneStream = await navigator.mediaDevices.getUserMedia({\n            audio: audioConstraints,\n            video: false\n          });\n        } catch (audioError) {\n          console.warn('Microphone access denied, recording without microphone');\n        }\n      }\n\n      // Get webcam stream if enabled\n      let webcamStreamLocal = null;\n      if (recordingSettings.includeWebcam) {\n        try {\n          const videoConstraints = {\n            width: {\n              ideal: 320\n            },\n            height: {\n              ideal: 240\n            },\n            frameRate: {\n              ideal: 30\n            }\n          };\n\n          // Add device ID if not default\n          if (recordingSettings.selectedCamera !== 'default') {\n            videoConstraints.deviceId = {\n              exact: recordingSettings.selectedCamera\n            };\n          }\n          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({\n            video: videoConstraints,\n            audio: false\n          });\n          setWebcamStream(webcamStreamLocal);\n\n          // Display webcam preview\n          if (webcamRef.current) {\n            webcamRef.current.srcObject = webcamStreamLocal;\n          }\n        } catch (webcamError) {\n          console.warn('Webcam access denied, recording without webcam');\n        }\n      }\n\n      // Combine all streams\n      let combinedStream = screenStream;\n\n      // Add microphone audio if available\n      if (microphoneStream) {\n        const audioTracks = microphoneStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n\n        // Setup audio level monitoring\n        setupAudioMonitoring(microphoneStream);\n      }\n\n      // Note: Webcam video will be composited in post-processing for now\n      // In a future version, we could use Canvas API to composite in real-time\n\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder with quality settings\n      const mimeType = getOptimalMimeType();\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType,\n        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)\n      });\n      const chunks = [];\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, {\n          type: mimeType\n        });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Clean up webcam stream\n        if (webcamStreamLocal) {\n          webcamStreamLocal.getTracks().forEach(track => track.stop());\n          setWebcamStream(null);\n        }\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n\n      // Start cursor tracking for smart zoom\n      startCursorTracking();\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n\n      // Stop webcam stream\n      if (webcamStream) {\n        webcamStream.getTracks().forEach(track => track.stop());\n        setWebcamStream(null);\n      }\n\n      // Clean up audio context\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n        analyserRef.current = null;\n      }\n\n      // Reset audio levels\n      setAudioLevels({\n        microphone: 0,\n        system: 0\n      });\n\n      // Stop cursor tracking\n      stopCursorTracking();\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Helper functions for Phase 2 features\n  const startCountdown = () => {\n    return new Promise(resolve => {\n      let count = countdown;\n      setStatus(`Recording starts in ${count}...`);\n      const countdownInterval = setInterval(() => {\n        count--;\n        if (count > 0) {\n          setStatus(`Recording starts in ${count}...`);\n        } else {\n          setStatus('Recording starting...');\n          clearInterval(countdownInterval);\n          resolve();\n        }\n      }, 1000);\n    });\n  };\n  const getOptimalMimeType = () => {\n    const types = ['video/webm;codecs=vp9,opus', 'video/webm;codecs=vp8,opus', 'video/webm;codecs=h264,opus', 'video/webm'];\n    for (const type of types) {\n      if (MediaRecorder.isTypeSupported(type)) {\n        return type;\n      }\n    }\n    return 'video/webm';\n  };\n  const getVideoBitrate = (quality, width, height) => {\n    const pixelCount = width * height;\n    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels\n\n    switch (quality) {\n      case 'draft':\n        return Math.floor(baseRate * 0.5) * 1000;\n      // 0.5 bits per pixel\n      case 'standard':\n        return Math.floor(baseRate * 1) * 1000;\n      // 1 bit per pixel\n      case 'high':\n        return Math.floor(baseRate * 2) * 1000;\n      // 2 bits per pixel\n      default:\n        return Math.floor(baseRate * 1) * 1000;\n    }\n  };\n  const setupAudioMonitoring = audioStream => {\n    if (!audioStream) return;\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const analyser = audioContext.createAnalyser();\n      const source = audioContext.createMediaStreamSource(audioStream);\n      analyser.fftSize = 256;\n      source.connect(analyser);\n      audioContextRef.current = audioContext;\n      analyserRef.current = analyser;\n\n      // Start monitoring audio levels\n      const monitorAudio = () => {\n        if (!analyserRef.current) return;\n        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\n        analyserRef.current.getByteFrequencyData(dataArray);\n\n        // Calculate average volume\n        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;\n        const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1\n\n        setAudioLevels(prev => ({\n          ...prev,\n          microphone: normalizedLevel\n        }));\n        if (isRecording) {\n          requestAnimationFrame(monitorAudio);\n        }\n      };\n      monitorAudio();\n    } catch (error) {\n      console.warn('Audio monitoring setup failed:', error);\n    }\n  };\n\n  // Settings modal functions\n  const openSettings = () => {\n    setTempSettings({\n      ...recordingSettings,\n      countdown\n    });\n    setShowSettings(true);\n    // Refresh device list when opening settings\n    loadAvailableDevices();\n  };\n  const closeSettings = () => {\n    setShowSettings(false);\n    setTempSettings(null);\n  };\n  const saveSettings = () => {\n    if (tempSettings) {\n      setRecordingSettings({\n        includeSystemAudio: tempSettings.includeSystemAudio,\n        includeMicrophone: tempSettings.includeMicrophone,\n        includeWebcam: tempSettings.includeWebcam,\n        resolution: tempSettings.resolution,\n        frameRate: tempSettings.frameRate,\n        quality: tempSettings.quality,\n        selectedMicrophone: tempSettings.selectedMicrophone,\n        selectedCamera: tempSettings.selectedCamera,\n        selectedSpeaker: tempSettings.selectedSpeaker\n      });\n      setCountdown(tempSettings.countdown);\n    }\n    setShowSettings(false);\n    setTempSettings(null);\n  };\n\n  // Phase 3: Cursor Tracking Functions\n  const startCursorTracking = () => {\n    if (!cursorTracking.enabled) return;\n\n    // Clear any existing tracking\n    stopCursorTracking();\n\n    // Track mouse movements and clicks\n    const trackMouseMovement = event => {\n      const timestamp = Date.now();\n      const position = {\n        x: event.screenX,\n        y: event.screenY,\n        timestamp,\n        velocity: calculateVelocity(event.screenX, event.screenY, timestamp)\n      };\n      setCursorTracking(prev => ({\n        ...prev,\n        positions: [...prev.positions.slice(-99), position] // Keep last 100 positions\n      }));\n    };\n    const trackMouseClick = event => {\n      const timestamp = Date.now();\n      const click = {\n        x: event.screenX,\n        y: event.screenY,\n        timestamp,\n        button: event.button,\n        type: event.type\n      };\n      setCursorTracking(prev => ({\n        ...prev,\n        clicks: [...prev.clicks.slice(-49), click] // Keep last 50 clicks\n      }));\n\n      // Analyze if this click should trigger a zoom\n      analyzeZoomTrigger(click);\n    };\n\n    // Add global event listeners\n    document.addEventListener('mousemove', trackMouseMovement);\n    document.addEventListener('click', trackMouseClick);\n    document.addEventListener('mousedown', trackMouseClick);\n    document.addEventListener('mouseup', trackMouseClick);\n\n    // Store cleanup function\n    cursorTrackingIntervalRef.current = () => {\n      document.removeEventListener('mousemove', trackMouseMovement);\n      document.removeEventListener('click', trackMouseClick);\n      document.removeEventListener('mousedown', trackMouseClick);\n      document.removeEventListener('mouseup', trackMouseClick);\n    };\n  };\n  const stopCursorTracking = () => {\n    if (cursorTrackingIntervalRef.current) {\n      cursorTrackingIntervalRef.current();\n      cursorTrackingIntervalRef.current = null;\n    }\n  };\n  const calculateVelocity = (x, y, timestamp) => {\n    const positions = cursorTracking.positions;\n    if (positions.length === 0) return 0;\n    const lastPosition = positions[positions.length - 1];\n    const deltaX = x - lastPosition.x;\n    const deltaY = y - lastPosition.y;\n    const deltaTime = timestamp - lastPosition.timestamp;\n    if (deltaTime === 0) return 0;\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    return distance / deltaTime; // pixels per millisecond\n  };\n  const analyzeZoomTrigger = click => {\n    // Simple zoom trigger logic - will enhance this\n    const recentActivity = calculateRecentActivity();\n    const shouldZoom = recentActivity.clickDensity > 0.5 || recentActivity.movementIntensity > 0.3;\n    if (shouldZoom && !cursorTracking.isZooming) {\n      triggerSmartZoom(click.x, click.y);\n    }\n  };\n  const calculateRecentActivity = () => {\n    const now = Date.now();\n    const timeWindow = 2000; // 2 seconds\n\n    const recentClicks = cursorTracking.clicks.filter(click => now - click.timestamp < timeWindow);\n    const recentPositions = cursorTracking.positions.filter(pos => now - pos.timestamp < timeWindow);\n    const clickDensity = recentClicks.length / (timeWindow / 1000); // clicks per second\n    const movementIntensity = recentPositions.reduce((sum, pos) => sum + pos.velocity, 0) / recentPositions.length;\n    return {\n      clickDensity,\n      movementIntensity\n    };\n  };\n  const triggerSmartZoom = (targetX, targetY) => {\n    setCursorTracking(prev => ({\n      ...prev,\n      isZooming: true,\n      currentZoom: {\n        x: targetX,\n        y: targetY,\n        scale: 1.5 // 1.5x zoom\n      }\n    }));\n\n    // Reset zoom after 3 seconds\n    setTimeout(() => {\n      setCursorTracking(prev => ({\n        ...prev,\n        isZooming: false,\n        currentZoom: {\n          x: 0,\n          y: 0,\n          scale: 1\n        }\n      }));\n    }, 3000);\n  };\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Screen Recorder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: checkPermissions,\n          children: \"\\uD83D\\uDD12 Check Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: openSettings,\n          disabled: isRecording,\n          children: \"\\u2699\\uFE0F Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 11\n        }, this), permissions.screen === 'denied' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: requestPermissions,\n          children: \"Grant Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: closeSettings,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Recording Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close\",\n            onClick: closeSettings,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Audio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.includeMicrophone) || false,\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  includeMicrophone: e.target.checked\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 19\n              }, this), \"Include Microphone\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 17\n            }, this), (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.includeMicrophone) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item device-selector\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Microphone Source:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.selectedMicrophone) || 'default',\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  selectedMicrophone: e.target.value\n                })),\n                children: availableDevices.microphones.map(device => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: device.id,\n                  children: device.label\n                }, device.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"setting-item disabled\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: false,\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 19\n              }, this), \"Include System Audio\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-note\",\n                children: \"(Coming in Phase 3)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.includeWebcam) || false,\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  includeWebcam: e.target.checked\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 19\n              }, this), \"Include Webcam\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 17\n            }, this), (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.includeWebcam) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item device-selector\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Camera Source:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.selectedCamera) || 'default',\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  selectedCamera: e.target.value\n                })),\n                children: availableDevices.cameras.map(device => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: device.id,\n                  children: device.label\n                }, device.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Resolution:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.resolution) || '1920x1080',\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  resolution: e.target.value\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1280x720\",\n                  children: \"720p (1280x720)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 788,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1920x1080\",\n                  children: \"1080p (1920x1080)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"2560x1440\",\n                  children: \"1440p (2560x1440)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"3840x2160\",\n                  children: \"4K (3840x2160)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Frame Rate:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.frameRate) || 30,\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  frameRate: parseInt(e.target.value)\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 30,\n                  children: \"30 FPS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 60,\n                  children: \"60 FPS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Quality:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.quality) || 'high',\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  quality: e.target.value\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"draft\",\n                  children: \"Draft (Smaller file)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 818,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"standard\",\n                  children: \"Standard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high\",\n                  children: \"High Quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Recording\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Countdown Timer (seconds):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.countdown) || 0,\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  countdown: parseInt(e.target.value)\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 0,\n                  children: \"No countdown\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 3,\n                  children: \"3 seconds\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 5,\n                  children: \"5 seconds\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 10,\n                  children: \"10 seconds\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: loadAvailableDevices,\n            children: \"\\uD83D\\uDD04 Refresh Devices\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-footer-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: closeSettings,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: saveSettings,\n              children: \"Save Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"source-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select Screen or Window:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sources-grid\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `source-item ${(selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.id) === source.id ? 'selected' : ''}`,\n            onClick: () => setSelectedSource(source),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: source.thumbnail,\n              alt: source.name,\n              className: \"source-thumbnail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"source-name\",\n              children: source.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 17\n            }, this)]\n          }, source.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 863,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-area\",\n        children: recordedBlob ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: videoRef,\n            className: \"preview-video\",\n            controls: true,\n            autoPlay: false,\n            preload: \"metadata\",\n            muted: true,\n            playsInline: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recording-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Recording completed \\u2022 Duration: \", formatDuration(recordingDuration)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status-box ${isRecording ? 'recording' : ''}`,\n            children: isRecording ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"recording-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"recording-dot\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 23\n                }, this), \"RECORDING\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-duration\",\n                children: formatDuration(recordingDuration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-source\",\n                children: selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-settings-info\",\n                children: [recordingSettings.resolution, \" \\u2022 \", recordingSettings.frameRate, \"fps \\u2022 \", recordingSettings.quality]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 21\n              }, this), (recordingSettings.includeMicrophone || recordingSettings.includeSystemAudio) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audio-levels\",\n                children: [recordingSettings.includeMicrophone && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audio-level-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audio-label\",\n                    children: \"\\uD83C\\uDFA4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audio-meter\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"audio-level-bar\",\n                      style: {\n                        width: `${audioLevels.microphone * 100}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 924,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 27\n                }, this), recordingSettings.includeSystemAudio && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audio-level-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audio-label\",\n                    children: \"\\uD83D\\uDD0A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 934,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audio-meter\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"audio-level-bar\",\n                      style: {\n                        width: `${audioLevels.system * 100}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 936,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 935,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ready-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-text\",\n                children: status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 21\n              }, this), selectedSource && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-source\",\n                children: [\"Ready to record: \", selectedSource.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 15\n          }, this), webcamStream && isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webcam-preview\",\n            children: [/*#__PURE__*/_jsxDEV(\"video\", {\n              ref: webcamRef,\n              className: \"webcam-video\",\n              autoPlay: true,\n              muted: true,\n              playsInline: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"webcam-label\",\n              children: \"Webcam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 883,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        children: [!isRecording && !recordedBlob && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: startRecording,\n          disabled: !selectedSource || permissions.screen === 'denied',\n          children: \"Start Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 13\n        }, this), isRecording && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger\",\n          onClick: stopRecording,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 985,\n          columnNumber: 13\n        }, this), recordedBlob && !isRecording && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [!showExportOptions ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowExportOptions(true),\n            children: \"Download Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"export-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Choose format:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('webm'),\n              children: \"Download WebM (Fast)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('mp4'),\n              children: \"Download MP4 (Compatible)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => setShowExportOptions(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearRecording,\n            children: \"New Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: loadSources,\n          children: \"Refresh Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 973,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 862,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 668,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"MWsamaszdqvUzjUbI2K4DsAGMfY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isRecording", "setIsRecording", "sources", "setSources", "selectedSource", "setSelectedSource", "recordedBlob", "setRecordedBlob", "permissions", "setPermissions", "status", "setStatus", "recordingDuration", "setRecordingDuration", "showExportOptions", "setShowExportOptions", "recordingSettings", "setRecordingSettings", "includeSystemAudio", "includeMicrophone", "includeWebcam", "resolution", "frameRate", "quality", "selectedMicrophone", "selectedCamera", "selectedSpeaker", "webcamStream", "setWebcamStream", "countdown", "setCountdown", "showSettings", "setShowSettings", "audioLevels", "setAudioLevels", "microphone", "system", "tempSettings", "setTempSettings", "availableDevices", "setAvailableDevices", "microphones", "cameras", "speakers", "cursorTracking", "setCursorTracking", "enabled", "positions", "clicks", "currentZoom", "x", "y", "scale", "isZooming", "videoRef", "webcamRef", "mediaRecorderRef", "streamRef", "durationIntervalRef", "audioContextRef", "analyserRef", "cursorTrackingIntervalRef", "zoomCanvasRef", "checkPermissions", "loadSources", "loadAvailableDevices", "devices", "navigator", "mediaDevices", "enumerateDevices", "filter", "device", "kind", "map", "id", "deviceId", "label", "slice", "groupId", "error", "console", "warn", "perms", "window", "electronAPI", "permissionStatus", "screen", "push", "camera", "join", "setTimeout", "requestPermissions", "granted", "getSources", "length", "find", "s", "name", "includes", "startRecording", "startCountdown", "current", "setInterval", "prev", "width", "height", "split", "Number", "screenConstraints", "audio", "video", "mandatory", "chromeMediaSource", "chromeMediaSourceId", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "minFrameRate", "maxFrameRate", "screenStream", "getUserMedia", "microphoneStream", "audioConstraints", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "exact", "audioError", "webcamStreamLocal", "videoConstraints", "ideal", "srcObject", "webcamError", "combinedStream", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "addTrack", "setupAudioMonitoring", "mimeType", "getOptimalMimeType", "mediaRecorder", "MediaRecorder", "videoBitsPerSecond", "getVideoBitrate", "chunks", "ondataavailable", "event", "data", "size", "onstop", "blob", "Blob", "type", "getTracks", "stop", "videoUrl", "URL", "createObjectURL", "src", "load", "start", "startCursorTracking", "clearInterval", "stopRecording", "close", "stopCursorTracking", "formatDuration", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "Promise", "resolve", "count", "countdownInterval", "types", "isTypeSupported", "pixelCount", "baseRate", "audioStream", "audioContext", "AudioContext", "webkitAudioContext", "analyser", "create<PERSON><PERSON>yser", "source", "createMediaStreamSource", "fftSize", "connect", "monitorAudio", "dataArray", "Uint8Array", "frequencyBinCount", "getByteFrequencyData", "average", "reduce", "sum", "value", "normalizedLevel", "min", "requestAnimationFrame", "openSettings", "closeSettings", "saveSettings", "trackMouseMovement", "timestamp", "Date", "now", "position", "screenX", "screenY", "velocity", "calculateVelocity", "trackMouseClick", "click", "button", "analyzeZoomTrigger", "document", "addEventListener", "removeEventListener", "lastPosition", "deltaX", "deltaY", "deltaTime", "distance", "sqrt", "recentActivity", "calculateRecentActivity", "shouldZoom", "clickDensity", "movementIntensity", "triggerSmartZoom", "timeWindow", "recentClicks", "recentPositions", "pos", "targetX", "targetY", "downloadRecording", "format", "url", "a", "createElement", "href", "download", "toISOString", "replace", "revokeObjectURL", "arrayBuffer", "uint8Array", "filename", "savedPath", "saveRecording", "clearRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "e", "stopPropagation", "checked", "onChange", "target", "parseInt", "thumbnail", "alt", "ref", "controls", "autoPlay", "preload", "muted", "playsInline", "style", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js"], "sourcesContent": ["/// <reference path=\"./electron.d.ts\" />\nimport { useState, useRef, useEffect } from 'react';\nimport './index.css';\n\nfunction App() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n\n  // New Phase 2 state variables\n  const [recordingSettings, setRecordingSettings] = useState({\n    includeSystemAudio: false,\n    includeMicrophone: true,\n    includeWebcam: false,\n    resolution: '1920x1080',\n    frameRate: 30,\n    quality: 'high',\n    selectedMicrophone: 'default',\n    selectedCamera: 'default',\n    selectedSpeaker: 'default'\n  });\n  const [webcamStream, setWebcamStream] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [audioLevels, setAudioLevels] = useState({ microphone: 0, system: 0 });\n  const [tempSettings, setTempSettings] = useState(null);\n  const [availableDevices, setAvailableDevices] = useState({\n    microphones: [],\n    cameras: [],\n    speakers: []\n  });\n\n  // Phase 3: Smart Zoom & Cursor Tracking state\n  const [cursorTracking, setCursorTracking] = useState({\n    enabled: true,\n    positions: [],\n    clicks: [],\n    currentZoom: { x: 0, y: 0, scale: 1 },\n    isZooming: false\n  });\n\n  const videoRef = useRef(null);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  const cursorTrackingIntervalRef = useRef(null);\n  const zoomCanvasRef = useRef(null);\n  \n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n    loadAvailableDevices();\n  }, []);\n\n  const loadAvailableDevices = async () => {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n\n      const microphones = devices\n        .filter(device => device.kind === 'audioinput')\n        .map(device => ({\n          id: device.deviceId,\n          label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,\n          groupId: device.groupId\n        }));\n\n      const cameras = devices\n        .filter(device => device.kind === 'videoinput')\n        .map(device => ({\n          id: device.deviceId,\n          label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,\n          groupId: device.groupId\n        }));\n\n      const speakers = devices\n        .filter(device => device.kind === 'audiooutput')\n        .map(device => ({\n          id: device.deviceId,\n          label: device.label || `Speaker ${device.deviceId.slice(0, 8)}`,\n          groupId: device.groupId\n        }));\n\n      setAvailableDevices({\n        microphones: [\n          { id: 'default', label: 'Default Microphone', groupId: null },\n          ...microphones\n        ],\n        cameras: [\n          { id: 'default', label: 'Default Camera', groupId: null },\n          ...cameras\n        ],\n        speakers: [\n          { id: 'default', label: 'Default Speaker', groupId: null },\n          ...speakers\n        ]\n      });\n    } catch (error) {\n      console.warn('Failed to enumerate devices:', error);\n      // Set default options if enumeration fails\n      setAvailableDevices({\n        microphones: [{ id: 'default', label: 'Default Microphone', groupId: null }],\n        cameras: [{ id: 'default', label: 'Default Camera', groupId: null }],\n        speakers: [{ id: 'default', label: 'Default Speaker', groupId: null }]\n      });\n    }\n  };\n  \n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n\n    // Show status message about permissions\n    const permissionStatus = [];\n    if (perms.screen === 'granted') permissionStatus.push('✅ Screen recording');\n    else permissionStatus.push('❌ Screen recording');\n\n    if (perms.microphone === 'granted') permissionStatus.push('✅ Microphone');\n    else permissionStatus.push('❌ Microphone');\n\n    if (perms.camera === 'granted') permissionStatus.push('✅ Camera');\n    else permissionStatus.push('❌ Camera');\n\n    setStatus(`Permissions: ${permissionStatus.join(', ')}`);\n\n    // Reset status after 3 seconds\n    setTimeout(() => {\n      if (!isRecording && !recordedBlob) {\n        setStatus('Ready to record');\n      }\n    }, 3000);\n  };\n  \n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  \n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  \n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n\n    try {\n      // Start countdown if enabled\n      if (countdown > 0) {\n        await startCountdown();\n      }\n\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Parse resolution settings\n      const [width, height] = recordingSettings.resolution.split('x').map(Number);\n\n      // Get screen stream (without system audio for now - will implement properly in Phase 3)\n      const screenConstraints = {\n        audio: false, // System audio capture needs more complex implementation\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: width,\n            maxWidth: width,\n            minHeight: height,\n            maxHeight: height,\n            minFrameRate: recordingSettings.frameRate,\n            maxFrameRate: recordingSettings.frameRate\n          }\n        }\n      };\n\n      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);\n\n      // Get microphone stream if enabled\n      let microphoneStream = null;\n      if (recordingSettings.includeMicrophone) {\n        try {\n          const audioConstraints = {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 48000\n          };\n\n          // Add device ID if not default\n          if (recordingSettings.selectedMicrophone !== 'default') {\n            audioConstraints.deviceId = { exact: recordingSettings.selectedMicrophone };\n          }\n\n          microphoneStream = await navigator.mediaDevices.getUserMedia({\n            audio: audioConstraints,\n            video: false\n          });\n        } catch (audioError) {\n          console.warn('Microphone access denied, recording without microphone');\n        }\n      }\n\n      // Get webcam stream if enabled\n      let webcamStreamLocal = null;\n      if (recordingSettings.includeWebcam) {\n        try {\n          const videoConstraints = {\n            width: { ideal: 320 },\n            height: { ideal: 240 },\n            frameRate: { ideal: 30 }\n          };\n\n          // Add device ID if not default\n          if (recordingSettings.selectedCamera !== 'default') {\n            videoConstraints.deviceId = { exact: recordingSettings.selectedCamera };\n          }\n\n          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({\n            video: videoConstraints,\n            audio: false\n          });\n          setWebcamStream(webcamStreamLocal);\n\n          // Display webcam preview\n          if (webcamRef.current) {\n            webcamRef.current.srcObject = webcamStreamLocal;\n          }\n        } catch (webcamError) {\n          console.warn('Webcam access denied, recording without webcam');\n        }\n      }\n\n      // Combine all streams\n      let combinedStream = screenStream;\n\n      // Add microphone audio if available\n      if (microphoneStream) {\n        const audioTracks = microphoneStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n\n        // Setup audio level monitoring\n        setupAudioMonitoring(microphoneStream);\n      }\n\n      // Note: Webcam video will be composited in post-processing for now\n      // In a future version, we could use Canvas API to composite in real-time\n\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder with quality settings\n      const mimeType = getOptimalMimeType();\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType,\n        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)\n      });\n\n      const chunks = [];\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, { type: mimeType });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Clean up webcam stream\n        if (webcamStreamLocal) {\n          webcamStreamLocal.getTracks().forEach(track => track.stop());\n          setWebcamStream(null);\n        }\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n\n      // Start cursor tracking for smart zoom\n      startCursorTracking();\n\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  \n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n\n      // Stop webcam stream\n      if (webcamStream) {\n        webcamStream.getTracks().forEach(track => track.stop());\n        setWebcamStream(null);\n      }\n\n      // Clean up audio context\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n        analyserRef.current = null;\n      }\n\n      // Reset audio levels\n      setAudioLevels({ microphone: 0, system: 0 });\n\n      // Stop cursor tracking\n      stopCursorTracking();\n\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  \n  const formatDuration = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Helper functions for Phase 2 features\n  const startCountdown = () => {\n    return new Promise((resolve) => {\n      let count = countdown;\n      setStatus(`Recording starts in ${count}...`);\n\n      const countdownInterval = setInterval(() => {\n        count--;\n        if (count > 0) {\n          setStatus(`Recording starts in ${count}...`);\n        } else {\n          setStatus('Recording starting...');\n          clearInterval(countdownInterval);\n          resolve();\n        }\n      }, 1000);\n    });\n  };\n\n  const getOptimalMimeType = () => {\n    const types = [\n      'video/webm;codecs=vp9,opus',\n      'video/webm;codecs=vp8,opus',\n      'video/webm;codecs=h264,opus',\n      'video/webm'\n    ];\n\n    for (const type of types) {\n      if (MediaRecorder.isTypeSupported(type)) {\n        return type;\n      }\n    }\n    return 'video/webm';\n  };\n\n  const getVideoBitrate = (quality, width, height) => {\n    const pixelCount = width * height;\n    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels\n\n    switch (quality) {\n      case 'draft':\n        return Math.floor(baseRate * 0.5) * 1000; // 0.5 bits per pixel\n      case 'standard':\n        return Math.floor(baseRate * 1) * 1000; // 1 bit per pixel\n      case 'high':\n        return Math.floor(baseRate * 2) * 1000; // 2 bits per pixel\n      default:\n        return Math.floor(baseRate * 1) * 1000;\n    }\n  };\n\n  const setupAudioMonitoring = (audioStream) => {\n    if (!audioStream) return;\n\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const analyser = audioContext.createAnalyser();\n      const source = audioContext.createMediaStreamSource(audioStream);\n\n      analyser.fftSize = 256;\n      source.connect(analyser);\n\n      audioContextRef.current = audioContext;\n      analyserRef.current = analyser;\n\n      // Start monitoring audio levels\n      const monitorAudio = () => {\n        if (!analyserRef.current) return;\n\n        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\n        analyserRef.current.getByteFrequencyData(dataArray);\n\n        // Calculate average volume\n        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;\n        const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1\n\n        setAudioLevels(prev => ({\n          ...prev,\n          microphone: normalizedLevel\n        }));\n\n        if (isRecording) {\n          requestAnimationFrame(monitorAudio);\n        }\n      };\n\n      monitorAudio();\n    } catch (error) {\n      console.warn('Audio monitoring setup failed:', error);\n    }\n  };\n\n  // Settings modal functions\n  const openSettings = () => {\n    setTempSettings({ ...recordingSettings, countdown });\n    setShowSettings(true);\n    // Refresh device list when opening settings\n    loadAvailableDevices();\n  };\n\n  const closeSettings = () => {\n    setShowSettings(false);\n    setTempSettings(null);\n  };\n\n  const saveSettings = () => {\n    if (tempSettings) {\n      setRecordingSettings({\n        includeSystemAudio: tempSettings.includeSystemAudio,\n        includeMicrophone: tempSettings.includeMicrophone,\n        includeWebcam: tempSettings.includeWebcam,\n        resolution: tempSettings.resolution,\n        frameRate: tempSettings.frameRate,\n        quality: tempSettings.quality,\n        selectedMicrophone: tempSettings.selectedMicrophone,\n        selectedCamera: tempSettings.selectedCamera,\n        selectedSpeaker: tempSettings.selectedSpeaker\n      });\n      setCountdown(tempSettings.countdown);\n    }\n    setShowSettings(false);\n    setTempSettings(null);\n  };\n\n  // Phase 3: Cursor Tracking Functions\n  const startCursorTracking = () => {\n    if (!cursorTracking.enabled) return;\n\n    // Clear any existing tracking\n    stopCursorTracking();\n\n    // Track mouse movements and clicks\n    const trackMouseMovement = (event) => {\n      const timestamp = Date.now();\n      const position = {\n        x: event.screenX,\n        y: event.screenY,\n        timestamp,\n        velocity: calculateVelocity(event.screenX, event.screenY, timestamp)\n      };\n\n      setCursorTracking(prev => ({\n        ...prev,\n        positions: [...prev.positions.slice(-99), position] // Keep last 100 positions\n      }));\n    };\n\n    const trackMouseClick = (event) => {\n      const timestamp = Date.now();\n      const click = {\n        x: event.screenX,\n        y: event.screenY,\n        timestamp,\n        button: event.button,\n        type: event.type\n      };\n\n      setCursorTracking(prev => ({\n        ...prev,\n        clicks: [...prev.clicks.slice(-49), click] // Keep last 50 clicks\n      }));\n\n      // Analyze if this click should trigger a zoom\n      analyzeZoomTrigger(click);\n    };\n\n    // Add global event listeners\n    document.addEventListener('mousemove', trackMouseMovement);\n    document.addEventListener('click', trackMouseClick);\n    document.addEventListener('mousedown', trackMouseClick);\n    document.addEventListener('mouseup', trackMouseClick);\n\n    // Store cleanup function\n    cursorTrackingIntervalRef.current = () => {\n      document.removeEventListener('mousemove', trackMouseMovement);\n      document.removeEventListener('click', trackMouseClick);\n      document.removeEventListener('mousedown', trackMouseClick);\n      document.removeEventListener('mouseup', trackMouseClick);\n    };\n  };\n\n  const stopCursorTracking = () => {\n    if (cursorTrackingIntervalRef.current) {\n      cursorTrackingIntervalRef.current();\n      cursorTrackingIntervalRef.current = null;\n    }\n  };\n\n  const calculateVelocity = (x, y, timestamp) => {\n    const positions = cursorTracking.positions;\n    if (positions.length === 0) return 0;\n\n    const lastPosition = positions[positions.length - 1];\n    const deltaX = x - lastPosition.x;\n    const deltaY = y - lastPosition.y;\n    const deltaTime = timestamp - lastPosition.timestamp;\n\n    if (deltaTime === 0) return 0;\n\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    return distance / deltaTime; // pixels per millisecond\n  };\n\n  const analyzeZoomTrigger = (click) => {\n    // Simple zoom trigger logic - will enhance this\n    const recentActivity = calculateRecentActivity();\n    const shouldZoom = recentActivity.clickDensity > 0.5 || recentActivity.movementIntensity > 0.3;\n\n    if (shouldZoom && !cursorTracking.isZooming) {\n      triggerSmartZoom(click.x, click.y);\n    }\n  };\n\n  const calculateRecentActivity = () => {\n    const now = Date.now();\n    const timeWindow = 2000; // 2 seconds\n\n    const recentClicks = cursorTracking.clicks.filter(\n      click => now - click.timestamp < timeWindow\n    );\n\n    const recentPositions = cursorTracking.positions.filter(\n      pos => now - pos.timestamp < timeWindow\n    );\n\n    const clickDensity = recentClicks.length / (timeWindow / 1000); // clicks per second\n    const movementIntensity = recentPositions.reduce((sum, pos) => sum + pos.velocity, 0) / recentPositions.length;\n\n    return { clickDensity, movementIntensity };\n  };\n\n  const triggerSmartZoom = (targetX, targetY) => {\n    setCursorTracking(prev => ({\n      ...prev,\n      isZooming: true,\n      currentZoom: {\n        x: targetX,\n        y: targetY,\n        scale: 1.5 // 1.5x zoom\n      }\n    }));\n\n    // Reset zoom after 3 seconds\n    setTimeout(() => {\n      setCursorTracking(prev => ({\n        ...prev,\n        isZooming: false,\n        currentZoom: { x: 0, y: 0, scale: 1 }\n      }));\n    }, 3000);\n  };\n\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  \n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  \n  return (\n    <div className=\"app\">\n      <header className=\"header\">\n        <h1>Screen Recorder</h1>\n        <div className=\"header-controls\">\n          <button\n            className=\"btn btn-secondary\"\n            onClick={checkPermissions}\n          >\n            🔒 Check Permissions\n          </button>\n          <button\n            className=\"btn btn-secondary\"\n            onClick={openSettings}\n            disabled={isRecording}\n          >\n            ⚙️ Settings\n          </button>\n          {permissions.screen === 'denied' && (\n            <button className=\"btn btn-primary\" onClick={requestPermissions}>\n              Grant Permissions\n            </button>\n          )}\n        </div>\n      </header>\n\n      {showSettings && (\n        <div className=\"modal-overlay\" onClick={closeSettings}>\n          <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n            <div className=\"modal-header\">\n              <h3>Recording Settings</h3>\n              <button className=\"modal-close\" onClick={closeSettings}>×</button>\n            </div>\n\n            <div className=\"modal-body\">\n              <div className=\"settings-group\">\n                <h4>Audio</h4>\n                <label className=\"setting-item\">\n                  <input\n                    type=\"checkbox\"\n                    checked={tempSettings?.includeMicrophone || false}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      includeMicrophone: e.target.checked\n                    }))}\n                  />\n                  Include Microphone\n                </label>\n\n                {tempSettings?.includeMicrophone && (\n                  <div className=\"setting-item device-selector\">\n                    <label>Microphone Source:</label>\n                    <select\n                      value={tempSettings?.selectedMicrophone || 'default'}\n                      onChange={(e) => setTempSettings(prev => ({\n                        ...prev,\n                        selectedMicrophone: e.target.value\n                      }))}\n                    >\n                      {availableDevices.microphones.map(device => (\n                        <option key={device.id} value={device.id}>\n                          {device.label}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                )}\n\n                <label className=\"setting-item disabled\">\n                  <input\n                    type=\"checkbox\"\n                    checked={false}\n                    disabled={true}\n                  />\n                  Include System Audio\n                  <span className=\"feature-note\">(Coming in Phase 3)</span>\n                </label>\n              </div>\n\n              <div className=\"settings-group\">\n                <h4>Video</h4>\n                <label className=\"setting-item\">\n                  <input\n                    type=\"checkbox\"\n                    checked={tempSettings?.includeWebcam || false}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      includeWebcam: e.target.checked\n                    }))}\n                  />\n                  Include Webcam\n                </label>\n\n                {tempSettings?.includeWebcam && (\n                  <div className=\"setting-item device-selector\">\n                    <label>Camera Source:</label>\n                    <select\n                      value={tempSettings?.selectedCamera || 'default'}\n                      onChange={(e) => setTempSettings(prev => ({\n                        ...prev,\n                        selectedCamera: e.target.value\n                      }))}\n                    >\n                      {availableDevices.cameras.map(device => (\n                        <option key={device.id} value={device.id}>\n                          {device.label}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                )}\n\n                <div className=\"setting-item\">\n                  <label>Resolution:</label>\n                  <select\n                    value={tempSettings?.resolution || '1920x1080'}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      resolution: e.target.value\n                    }))}\n                  >\n                    <option value=\"1280x720\">720p (1280x720)</option>\n                    <option value=\"1920x1080\">1080p (1920x1080)</option>\n                    <option value=\"2560x1440\">1440p (2560x1440)</option>\n                    <option value=\"3840x2160\">4K (3840x2160)</option>\n                  </select>\n                </div>\n\n                <div className=\"setting-item\">\n                  <label>Frame Rate:</label>\n                  <select\n                    value={tempSettings?.frameRate || 30}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      frameRate: parseInt(e.target.value)\n                    }))}\n                  >\n                    <option value={30}>30 FPS</option>\n                    <option value={60}>60 FPS</option>\n                  </select>\n                </div>\n\n                <div className=\"setting-item\">\n                  <label>Quality:</label>\n                  <select\n                    value={tempSettings?.quality || 'high'}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      quality: e.target.value\n                    }))}\n                  >\n                    <option value=\"draft\">Draft (Smaller file)</option>\n                    <option value=\"standard\">Standard</option>\n                    <option value=\"high\">High Quality</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"settings-group\">\n                <h4>Recording</h4>\n                <div className=\"setting-item\">\n                  <label>Countdown Timer (seconds):</label>\n                  <select\n                    value={tempSettings?.countdown || 0}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      countdown: parseInt(e.target.value)\n                    }))}\n                  >\n                    <option value={0}>No countdown</option>\n                    <option value={3}>3 seconds</option>\n                    <option value={5}>5 seconds</option>\n                    <option value={10}>10 seconds</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"modal-footer\">\n              <button className=\"btn btn-secondary\" onClick={loadAvailableDevices}>\n                🔄 Refresh Devices\n              </button>\n              <div className=\"modal-footer-right\">\n                <button className=\"btn btn-secondary\" onClick={closeSettings}>\n                  Cancel\n                </button>\n                <button className=\"btn btn-primary\" onClick={saveSettings}>\n                  Save Settings\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <main className=\"content\">\n        <div className=\"source-selector\">\n          <h3>Select Screen or Window:</h3>\n          <div className=\"sources-grid\">\n            {sources.map((source) => (\n              <div\n                key={source.id}\n                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}\n                onClick={() => setSelectedSource(source)}\n              >\n                <img\n                  src={source.thumbnail}\n                  alt={source.name}\n                  className=\"source-thumbnail\"\n                />\n                <div className=\"source-name\">{source.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"recording-area\">\n          {recordedBlob ? (\n            <div className=\"preview-container\">\n              <video\n                ref={videoRef}\n                className=\"preview-video\"\n                controls\n                autoPlay={false}\n                preload=\"metadata\"\n                muted\n                playsInline\n              />\n              <div className=\"recording-info\">\n                <p>Recording completed • Duration: {formatDuration(recordingDuration)}</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"status-display\">\n              <div className={`status-box ${isRecording ? 'recording' : ''}`}>\n                {isRecording ? (\n                  <div className=\"recording-status\">\n                    <div className=\"recording-indicator\">\n                      <span className=\"recording-dot\"></span>\n                      RECORDING\n                    </div>\n                    <div className=\"recording-duration\">\n                      {formatDuration(recordingDuration)}\n                    </div>\n                    <div className=\"recording-source\">\n                      {selectedSource?.name}\n                    </div>\n                    <div className=\"recording-settings-info\">\n                      {recordingSettings.resolution} • {recordingSettings.frameRate}fps • {recordingSettings.quality}\n                    </div>\n\n                    {/* Audio level indicators */}\n                    {(recordingSettings.includeMicrophone || recordingSettings.includeSystemAudio) && (\n                      <div className=\"audio-levels\">\n                        {recordingSettings.includeMicrophone && (\n                          <div className=\"audio-level-item\">\n                            <span className=\"audio-label\">🎤</span>\n                            <div className=\"audio-meter\">\n                              <div\n                                className=\"audio-level-bar\"\n                                style={{ width: `${audioLevels.microphone * 100}%` }}\n                              />\n                            </div>\n                          </div>\n                        )}\n                        {recordingSettings.includeSystemAudio && (\n                          <div className=\"audio-level-item\">\n                            <span className=\"audio-label\">🔊</span>\n                            <div className=\"audio-meter\">\n                              <div\n                                className=\"audio-level-bar\"\n                                style={{ width: `${audioLevels.system * 100}%` }}\n                              />\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"ready-status\">\n                    <div className=\"status-text\">{status}</div>\n                    {selectedSource && (\n                      <div className=\"selected-source\">Ready to record: {selectedSource.name}</div>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              {/* Webcam preview during recording */}\n              {webcamStream && isRecording && (\n                <div className=\"webcam-preview\">\n                  <video\n                    ref={webcamRef}\n                    className=\"webcam-video\"\n                    autoPlay\n                    muted\n                    playsInline\n                  />\n                  <div className=\"webcam-label\">Webcam</div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n        \n        <div className=\"controls\">\n          {!isRecording && !recordedBlob && (\n            <button\n              className=\"btn btn-primary\"\n              onClick={startRecording}\n              disabled={!selectedSource || permissions.screen === 'denied'}\n            >\n              Start Recording\n            </button>\n          )}\n          \n          {isRecording && (\n            <button className=\"btn btn-danger\" onClick={stopRecording}>\n              Stop Recording\n            </button>\n          )}\n          \n          {recordedBlob && !isRecording && (\n            <>\n              {!showExportOptions ? (\n                <button \n                  className=\"btn btn-primary\" \n                  onClick={() => setShowExportOptions(true)}\n                >\n                  Download Recording\n                </button>\n              ) : (\n                <div className=\"export-options\">\n                  <h4>Choose format:</h4>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('webm')}\n                  >\n                    Download WebM (Fast)\n                  </button>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('mp4')}\n                  >\n                    Download MP4 (Compatible)\n                  </button>\n                  <button \n                    className=\"btn btn-secondary\" \n                    onClick={() => setShowExportOptions(false)}\n                  >\n                    Cancel\n                  </button>\n                </div>\n              )}\n              <button className=\"btn btn-secondary\" onClick={clearRecording}>\n                New Recording\n              </button>\n            </>\n          )}\n          \n          <button className=\"btn btn-secondary\" onClick={loadSources}>\n            Refresh Sources\n          </button>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,iBAAiB,CAAC;EACvD,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC;IACzD2B,kBAAkB,EAAE,KAAK;IACzBC,iBAAiB,EAAE,IAAI;IACvBC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,WAAW;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,MAAM;IACfC,kBAAkB,EAAE,SAAS;IAC7BC,cAAc,EAAE,SAAS;IACzBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC;IAAE4C,UAAU,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAC5E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC;IACvDkD,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC;IACnDuD,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,EAAE;IACbC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC;IACrCC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAG9D,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM+D,SAAS,GAAG/D,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgE,gBAAgB,GAAGhE,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMiE,SAAS,GAAGjE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkE,mBAAmB,GAAGlE,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMmE,eAAe,GAAGnE,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMoE,WAAW,GAAGpE,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMqE,yBAAyB,GAAGrE,MAAM,CAAC,IAAI,CAAC;EAC9C,MAAMsE,aAAa,GAAGtE,MAAM,CAAC,IAAI,CAAC;EAElCC,SAAS,CAAC,MAAM;IACdsE,gBAAgB,CAAC,CAAC;IAClBC,WAAW,CAAC,CAAC;IACbC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,OAAO,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,gBAAgB,CAAC,CAAC;MAE/D,MAAM5B,WAAW,GAAGyB,OAAO,CACxBI,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC,CAC9CC,GAAG,CAACF,MAAM,KAAK;QACdG,EAAE,EAAEH,MAAM,CAACI,QAAQ;QACnBC,KAAK,EAAEL,MAAM,CAACK,KAAK,IAAI,cAAcL,MAAM,CAACI,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAClEC,OAAO,EAAEP,MAAM,CAACO;MAClB,CAAC,CAAC,CAAC;MAEL,MAAMpC,OAAO,GAAGwB,OAAO,CACpBI,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC,CAC9CC,GAAG,CAACF,MAAM,KAAK;QACdG,EAAE,EAAEH,MAAM,CAACI,QAAQ;QACnBC,KAAK,EAAEL,MAAM,CAACK,KAAK,IAAI,UAAUL,MAAM,CAACI,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DC,OAAO,EAAEP,MAAM,CAACO;MAClB,CAAC,CAAC,CAAC;MAEL,MAAMnC,QAAQ,GAAGuB,OAAO,CACrBI,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,aAAa,CAAC,CAC/CC,GAAG,CAACF,MAAM,KAAK;QACdG,EAAE,EAAEH,MAAM,CAACI,QAAQ;QACnBC,KAAK,EAAEL,MAAM,CAACK,KAAK,IAAI,WAAWL,MAAM,CAACI,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/DC,OAAO,EAAEP,MAAM,CAACO;MAClB,CAAC,CAAC,CAAC;MAELtC,mBAAmB,CAAC;QAClBC,WAAW,EAAE,CACX;UAAEiC,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,oBAAoB;UAAEE,OAAO,EAAE;QAAK,CAAC,EAC7D,GAAGrC,WAAW,CACf;QACDC,OAAO,EAAE,CACP;UAAEgC,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,gBAAgB;UAAEE,OAAO,EAAE;QAAK,CAAC,EACzD,GAAGpC,OAAO,CACX;QACDC,QAAQ,EAAE,CACR;UAAE+B,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,iBAAiB;UAAEE,OAAO,EAAE;QAAK,CAAC,EAC1D,GAAGnC,QAAQ;MAEf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEF,KAAK,CAAC;MACnD;MACAvC,mBAAmB,CAAC;QAClBC,WAAW,EAAE,CAAC;UAAEiC,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,oBAAoB;UAAEE,OAAO,EAAE;QAAK,CAAC,CAAC;QAC5EpC,OAAO,EAAE,CAAC;UAAEgC,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,gBAAgB;UAAEE,OAAO,EAAE;QAAK,CAAC,CAAC;QACpEnC,QAAQ,EAAE,CAAC;UAAE+B,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,iBAAiB;UAAEE,OAAO,EAAE;QAAK,CAAC;MACvE,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMf,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMmB,KAAK,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACrB,gBAAgB,CAAC,CAAC;IACzDtD,cAAc,CAACyE,KAAK,CAAC;;IAErB;IACA,MAAMG,gBAAgB,GAAG,EAAE;IAC3B,IAAIH,KAAK,CAACI,MAAM,KAAK,SAAS,EAAED,gBAAgB,CAACE,IAAI,CAAC,oBAAoB,CAAC,CAAC,KACvEF,gBAAgB,CAACE,IAAI,CAAC,oBAAoB,CAAC;IAEhD,IAAIL,KAAK,CAAC/C,UAAU,KAAK,SAAS,EAAEkD,gBAAgB,CAACE,IAAI,CAAC,cAAc,CAAC,CAAC,KACrEF,gBAAgB,CAACE,IAAI,CAAC,cAAc,CAAC;IAE1C,IAAIL,KAAK,CAACM,MAAM,KAAK,SAAS,EAAEH,gBAAgB,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC,KAC7DF,gBAAgB,CAACE,IAAI,CAAC,UAAU,CAAC;IAEtC5E,SAAS,CAAC,gBAAgB0E,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;;IAExD;IACAC,UAAU,CAAC,MAAM;MACf,IAAI,CAAC1F,WAAW,IAAI,CAACM,YAAY,EAAE;QACjCK,SAAS,CAAC,iBAAiB,CAAC;MAC9B;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMgF,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,OAAO,GAAG,MAAMT,MAAM,CAACC,WAAW,CAACO,kBAAkB,CAAC,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACX,MAAM7B,gBAAgB,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAM9D,OAAO,GAAG,MAAMiF,MAAM,CAACC,WAAW,CAACS,UAAU,CAAC,CAAC;IACrD1F,UAAU,CAACD,OAAO,CAAC;IACnB,IAAIA,OAAO,CAAC4F,MAAM,GAAG,CAAC,IAAI,CAAC1F,cAAc,EAAE;MACzC;MACA,MAAMkF,MAAM,GAAGpF,OAAO,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;MACxF7F,iBAAiB,CAACiF,MAAM,IAAIpF,OAAO,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMiG,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAC/F,cAAc,EAAE;MACnBO,SAAS,CAAC,4CAA4C,CAAC;MACvD;IACF;IAEA,IAAI;MACF;MACA,IAAIkB,SAAS,GAAG,CAAC,EAAE;QACjB,MAAMuE,cAAc,CAAC,CAAC;MACxB;MAEAzF,SAAS,CAAC,uBAAuB,CAAC;MAClCE,oBAAoB,CAAC,CAAC,CAAC;;MAEvB;MACA6C,mBAAmB,CAAC2C,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC9CzF,oBAAoB,CAAC0F,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAGzF,iBAAiB,CAACK,UAAU,CAACqF,KAAK,CAAC,GAAG,CAAC,CAACjC,GAAG,CAACkC,MAAM,CAAC;;MAE3E;MACA,MAAMC,iBAAiB,GAAG;QACxBC,KAAK,EAAE,KAAK;QAAE;QACdC,KAAK,EAAE;UACLC,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAE7G,cAAc,CAACsE,EAAE;YACtCwC,QAAQ,EAAEV,KAAK;YACfW,QAAQ,EAAEX,KAAK;YACfY,SAAS,EAAEX,MAAM;YACjBY,SAAS,EAAEZ,MAAM;YACjBa,YAAY,EAAEtG,iBAAiB,CAACM,SAAS;YACzCiG,YAAY,EAAEvG,iBAAiB,CAACM;UAClC;QACF;MACF,CAAC;MAED,MAAMkG,YAAY,GAAG,MAAMrD,SAAS,CAACC,YAAY,CAACqD,YAAY,CAACb,iBAAiB,CAAC;;MAEjF;MACA,IAAIc,gBAAgB,GAAG,IAAI;MAC3B,IAAI1G,iBAAiB,CAACG,iBAAiB,EAAE;QACvC,IAAI;UACF,MAAMwG,gBAAgB,GAAG;YACvBC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE;UACd,CAAC;;UAED;UACA,IAAI/G,iBAAiB,CAACQ,kBAAkB,KAAK,SAAS,EAAE;YACtDmG,gBAAgB,CAAChD,QAAQ,GAAG;cAAEqD,KAAK,EAAEhH,iBAAiB,CAACQ;YAAmB,CAAC;UAC7E;UAEAkG,gBAAgB,GAAG,MAAMvD,SAAS,CAACC,YAAY,CAACqD,YAAY,CAAC;YAC3DZ,KAAK,EAAEc,gBAAgB;YACvBb,KAAK,EAAE;UACT,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOmB,UAAU,EAAE;UACnBjD,OAAO,CAACC,IAAI,CAAC,wDAAwD,CAAC;QACxE;MACF;;MAEA;MACA,IAAIiD,iBAAiB,GAAG,IAAI;MAC5B,IAAIlH,iBAAiB,CAACI,aAAa,EAAE;QACnC,IAAI;UACF,MAAM+G,gBAAgB,GAAG;YACvB3B,KAAK,EAAE;cAAE4B,KAAK,EAAE;YAAI,CAAC;YACrB3B,MAAM,EAAE;cAAE2B,KAAK,EAAE;YAAI,CAAC;YACtB9G,SAAS,EAAE;cAAE8G,KAAK,EAAE;YAAG;UACzB,CAAC;;UAED;UACA,IAAIpH,iBAAiB,CAACS,cAAc,KAAK,SAAS,EAAE;YAClD0G,gBAAgB,CAACxD,QAAQ,GAAG;cAAEqD,KAAK,EAAEhH,iBAAiB,CAACS;YAAe,CAAC;UACzE;UAEAyG,iBAAiB,GAAG,MAAM/D,SAAS,CAACC,YAAY,CAACqD,YAAY,CAAC;YAC5DX,KAAK,EAAEqB,gBAAgB;YACvBtB,KAAK,EAAE;UACT,CAAC,CAAC;UACFjF,eAAe,CAACsG,iBAAiB,CAAC;;UAElC;UACA,IAAI3E,SAAS,CAAC8C,OAAO,EAAE;YACrB9C,SAAS,CAAC8C,OAAO,CAACgC,SAAS,GAAGH,iBAAiB;UACjD;QACF,CAAC,CAAC,OAAOI,WAAW,EAAE;UACpBtD,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;QAChE;MACF;;MAEA;MACA,IAAIsD,cAAc,GAAGf,YAAY;;MAEjC;MACA,IAAIE,gBAAgB,EAAE;QACpB,MAAMc,WAAW,GAAGd,gBAAgB,CAACe,cAAc,CAAC,CAAC;QACrDD,WAAW,CAACE,OAAO,CAACC,KAAK,IAAIJ,cAAc,CAACK,QAAQ,CAACD,KAAK,CAAC,CAAC;;QAE5D;QACAE,oBAAoB,CAACnB,gBAAgB,CAAC;MACxC;;MAEA;MACA;;MAEAjE,SAAS,CAAC4C,OAAO,GAAGkC,cAAc;;MAElC;MACA,MAAMO,QAAQ,GAAGC,kBAAkB,CAAC,CAAC;MACrC,MAAMC,aAAa,GAAG,IAAIC,aAAa,CAACV,cAAc,EAAE;QACtDO,QAAQ;QACRI,kBAAkB,EAAEC,eAAe,CAACnI,iBAAiB,CAACO,OAAO,EAAEiF,KAAK,EAAEC,MAAM;MAC9E,CAAC,CAAC;MAEF,MAAM2C,MAAM,GAAG,EAAE;MAEjBJ,aAAa,CAACK,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBJ,MAAM,CAAC7D,IAAI,CAAC+D,KAAK,CAACC,IAAI,CAAC;QACzB;MACF,CAAC;MAEDP,aAAa,CAACS,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACP,MAAM,EAAE;UAAEQ,IAAI,EAAEd;QAAS,CAAC,CAAC;QACjDvI,eAAe,CAACmJ,IAAI,CAAC;QACrB/I,SAAS,CAAC,2CAA2C,CAAC;;QAEtD;QACA,IAAIuH,iBAAiB,EAAE;UACrBA,iBAAiB,CAAC2B,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;UAC5DlI,eAAe,CAAC,IAAI,CAAC;QACvB;;QAEA;QACA8D,UAAU,CAAC,MAAM;UACf,IAAIpC,QAAQ,CAAC+C,OAAO,IAAIqD,IAAI,EAAE;YAC5B,MAAMK,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;YAC1CpG,QAAQ,CAAC+C,OAAO,CAAC6D,GAAG,GAAGH,QAAQ;YAC/BzG,QAAQ,CAAC+C,OAAO,CAAC8D,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3B;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MAED3G,gBAAgB,CAAC6C,OAAO,GAAG2C,aAAa;MACxCA,aAAa,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE1BnK,cAAc,CAAC,IAAI,CAAC;MACpBU,SAAS,CAAC,uBAAuB,CAAC;;MAElC;MACA0J,mBAAmB,CAAC,CAAC;IAEvB,CAAC,CAAC,OAAOtF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDpE,SAAS,CAAC,sDAAsD,CAAC;MACjE,IAAI+C,mBAAmB,CAAC2C,OAAO,EAAE;QAC/BiE,aAAa,CAAC5G,mBAAmB,CAAC2C,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMkE,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI/G,gBAAgB,CAAC6C,OAAO,IAAIrG,WAAW,EAAE;MAC3CwD,gBAAgB,CAAC6C,OAAO,CAACyD,IAAI,CAAC,CAAC;;MAE/B;MACA,IAAIpG,mBAAmB,CAAC2C,OAAO,EAAE;QAC/BiE,aAAa,CAAC5G,mBAAmB,CAAC2C,OAAO,CAAC;MAC5C;;MAEA;MACA,IAAI5C,SAAS,CAAC4C,OAAO,EAAE;QACrB5C,SAAS,CAAC4C,OAAO,CAACwD,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;MAC9D;;MAEA;MACA,IAAInI,YAAY,EAAE;QAChBA,YAAY,CAACkI,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;QACvDlI,eAAe,CAAC,IAAI,CAAC;MACvB;;MAEA;MACA,IAAI+B,eAAe,CAAC0C,OAAO,EAAE;QAC3B1C,eAAe,CAAC0C,OAAO,CAACmE,KAAK,CAAC,CAAC;QAC/B7G,eAAe,CAAC0C,OAAO,GAAG,IAAI;QAC9BzC,WAAW,CAACyC,OAAO,GAAG,IAAI;MAC5B;;MAEA;MACAnE,cAAc,CAAC;QAAEC,UAAU,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,CAAC;;MAE5C;MACAqI,kBAAkB,CAAC,CAAC;MAEpBxK,cAAc,CAAC,KAAK,CAAC;MACrBU,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC;EAED,MAAM+J,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAM7E,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAI8E,OAAO,CAAEC,OAAO,IAAK;MAC9B,IAAIC,KAAK,GAAGvJ,SAAS;MACrBlB,SAAS,CAAC,uBAAuByK,KAAK,KAAK,CAAC;MAE5C,MAAMC,iBAAiB,GAAG/E,WAAW,CAAC,MAAM;QAC1C8E,KAAK,EAAE;QACP,IAAIA,KAAK,GAAG,CAAC,EAAE;UACbzK,SAAS,CAAC,uBAAuByK,KAAK,KAAK,CAAC;QAC9C,CAAC,MAAM;UACLzK,SAAS,CAAC,uBAAuB,CAAC;UAClC2J,aAAa,CAACe,iBAAiB,CAAC;UAChCF,OAAO,CAAC,CAAC;QACX;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMpC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMuC,KAAK,GAAG,CACZ,4BAA4B,EAC5B,4BAA4B,EAC5B,6BAA6B,EAC7B,YAAY,CACb;IAED,KAAK,MAAM1B,IAAI,IAAI0B,KAAK,EAAE;MACxB,IAAIrC,aAAa,CAACsC,eAAe,CAAC3B,IAAI,CAAC,EAAE;QACvC,OAAOA,IAAI;MACb;IACF;IACA,OAAO,YAAY;EACrB,CAAC;EAED,MAAMT,eAAe,GAAGA,CAAC5H,OAAO,EAAEiF,KAAK,EAAEC,MAAM,KAAK;IAClD,MAAM+E,UAAU,GAAGhF,KAAK,GAAGC,MAAM;IACjC,MAAMgF,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC,CAAC;;IAEpC,QAAQjK,OAAO;MACb,KAAK,OAAO;QACV,OAAOsJ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI;MAAE;MAC5C,KAAK,UAAU;QACb,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;MAAE;MAC1C,KAAK,MAAM;QACT,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;MAAE;MAC1C;QACE,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;IAC1C;EACF,CAAC;EAED,MAAM5C,oBAAoB,GAAI6C,WAAW,IAAK;IAC5C,IAAI,CAACA,WAAW,EAAE;IAElB,IAAI;MACF,MAAMC,YAAY,GAAG,KAAKxG,MAAM,CAACyG,YAAY,IAAIzG,MAAM,CAAC0G,kBAAkB,EAAE,CAAC;MAC7E,MAAMC,QAAQ,GAAGH,YAAY,CAACI,cAAc,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAGL,YAAY,CAACM,uBAAuB,CAACP,WAAW,CAAC;MAEhEI,QAAQ,CAACI,OAAO,GAAG,GAAG;MACtBF,MAAM,CAACG,OAAO,CAACL,QAAQ,CAAC;MAExBnI,eAAe,CAAC0C,OAAO,GAAGsF,YAAY;MACtC/H,WAAW,CAACyC,OAAO,GAAGyF,QAAQ;;MAE9B;MACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAI,CAACxI,WAAW,CAACyC,OAAO,EAAE;QAE1B,MAAMgG,SAAS,GAAG,IAAIC,UAAU,CAAC1I,WAAW,CAACyC,OAAO,CAACkG,iBAAiB,CAAC;QACvE3I,WAAW,CAACyC,OAAO,CAACmG,oBAAoB,CAACH,SAAS,CAAC;;QAEnD;QACA,MAAMI,OAAO,GAAGJ,SAAS,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACvG,MAAM;QACnF,MAAM+G,eAAe,GAAGhC,IAAI,CAACiC,GAAG,CAACL,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEpDvK,cAAc,CAACqE,IAAI,KAAK;UACtB,GAAGA,IAAI;UACPpE,UAAU,EAAE0K;QACd,CAAC,CAAC,CAAC;QAEH,IAAI7M,WAAW,EAAE;UACf+M,qBAAqB,CAACX,YAAY,CAAC;QACrC;MACF,CAAC;MAEDA,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOrH,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,gCAAgC,EAAEF,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMiI,YAAY,GAAGA,CAAA,KAAM;IACzB1K,eAAe,CAAC;MAAE,GAAGtB,iBAAiB;MAAEa;IAAU,CAAC,CAAC;IACpDG,eAAe,CAAC,IAAI,CAAC;IACrB;IACAiC,oBAAoB,CAAC,CAAC;EACxB,CAAC;EAED,MAAMgJ,aAAa,GAAGA,CAAA,KAAM;IAC1BjL,eAAe,CAAC,KAAK,CAAC;IACtBM,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4K,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI7K,YAAY,EAAE;MAChBpB,oBAAoB,CAAC;QACnBC,kBAAkB,EAAEmB,YAAY,CAACnB,kBAAkB;QACnDC,iBAAiB,EAAEkB,YAAY,CAAClB,iBAAiB;QACjDC,aAAa,EAAEiB,YAAY,CAACjB,aAAa;QACzCC,UAAU,EAAEgB,YAAY,CAAChB,UAAU;QACnCC,SAAS,EAAEe,YAAY,CAACf,SAAS;QACjCC,OAAO,EAAEc,YAAY,CAACd,OAAO;QAC7BC,kBAAkB,EAAEa,YAAY,CAACb,kBAAkB;QACnDC,cAAc,EAAEY,YAAY,CAACZ,cAAc;QAC3CC,eAAe,EAAEW,YAAY,CAACX;MAChC,CAAC,CAAC;MACFI,YAAY,CAACO,YAAY,CAACR,SAAS,CAAC;IACtC;IACAG,eAAe,CAAC,KAAK,CAAC;IACtBM,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM+H,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACzH,cAAc,CAACE,OAAO,EAAE;;IAE7B;IACA2H,kBAAkB,CAAC,CAAC;;IAEpB;IACA,MAAM0C,kBAAkB,GAAI7D,KAAK,IAAK;MACpC,MAAM8D,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,MAAMC,QAAQ,GAAG;QACfrK,CAAC,EAAEoG,KAAK,CAACkE,OAAO;QAChBrK,CAAC,EAAEmG,KAAK,CAACmE,OAAO;QAChBL,SAAS;QACTM,QAAQ,EAAEC,iBAAiB,CAACrE,KAAK,CAACkE,OAAO,EAAElE,KAAK,CAACmE,OAAO,EAAEL,SAAS;MACrE,CAAC;MAEDvK,iBAAiB,CAAC0D,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPxD,SAAS,EAAE,CAAC,GAAGwD,IAAI,CAACxD,SAAS,CAAC8B,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE0I,QAAQ,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAMK,eAAe,GAAItE,KAAK,IAAK;MACjC,MAAM8D,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,MAAMO,KAAK,GAAG;QACZ3K,CAAC,EAAEoG,KAAK,CAACkE,OAAO;QAChBrK,CAAC,EAAEmG,KAAK,CAACmE,OAAO;QAChBL,SAAS;QACTU,MAAM,EAAExE,KAAK,CAACwE,MAAM;QACpBlE,IAAI,EAAEN,KAAK,CAACM;MACd,CAAC;MAED/G,iBAAiB,CAAC0D,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPvD,MAAM,EAAE,CAAC,GAAGuD,IAAI,CAACvD,MAAM,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEgJ,KAAK,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC;;MAEH;MACAE,kBAAkB,CAACF,KAAK,CAAC;IAC3B,CAAC;;IAED;IACAG,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEd,kBAAkB,CAAC;IAC1Da,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEL,eAAe,CAAC;IACnDI,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,eAAe,CAAC;IACvDI,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEL,eAAe,CAAC;;IAErD;IACA/J,yBAAyB,CAACwC,OAAO,GAAG,MAAM;MACxC2H,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEf,kBAAkB,CAAC;MAC7Da,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEN,eAAe,CAAC;MACtDI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,eAAe,CAAC;MAC1DI,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEN,eAAe,CAAC;IAC1D,CAAC;EACH,CAAC;EAED,MAAMnD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI5G,yBAAyB,CAACwC,OAAO,EAAE;MACrCxC,yBAAyB,CAACwC,OAAO,CAAC,CAAC;MACnCxC,yBAAyB,CAACwC,OAAO,GAAG,IAAI;IAC1C;EACF,CAAC;EAED,MAAMsH,iBAAiB,GAAGA,CAACzK,CAAC,EAAEC,CAAC,EAAEiK,SAAS,KAAK;IAC7C,MAAMrK,SAAS,GAAGH,cAAc,CAACG,SAAS;IAC1C,IAAIA,SAAS,CAAC+C,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAEpC,MAAMqI,YAAY,GAAGpL,SAAS,CAACA,SAAS,CAAC+C,MAAM,GAAG,CAAC,CAAC;IACpD,MAAMsI,MAAM,GAAGlL,CAAC,GAAGiL,YAAY,CAACjL,CAAC;IACjC,MAAMmL,MAAM,GAAGlL,CAAC,GAAGgL,YAAY,CAAChL,CAAC;IACjC,MAAMmL,SAAS,GAAGlB,SAAS,GAAGe,YAAY,CAACf,SAAS;IAEpD,IAAIkB,SAAS,KAAK,CAAC,EAAE,OAAO,CAAC;IAE7B,MAAMC,QAAQ,GAAG1D,IAAI,CAAC2D,IAAI,CAACJ,MAAM,GAAGA,MAAM,GAAGC,MAAM,GAAGA,MAAM,CAAC;IAC7D,OAAOE,QAAQ,GAAGD,SAAS,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMP,kBAAkB,GAAIF,KAAK,IAAK;IACpC;IACA,MAAMY,cAAc,GAAGC,uBAAuB,CAAC,CAAC;IAChD,MAAMC,UAAU,GAAGF,cAAc,CAACG,YAAY,GAAG,GAAG,IAAIH,cAAc,CAACI,iBAAiB,GAAG,GAAG;IAE9F,IAAIF,UAAU,IAAI,CAAC/L,cAAc,CAACS,SAAS,EAAE;MAC3CyL,gBAAgB,CAACjB,KAAK,CAAC3K,CAAC,EAAE2K,KAAK,CAAC1K,CAAC,CAAC;IACpC;EACF,CAAC;EAED,MAAMuL,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMpB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,MAAMyB,UAAU,GAAG,IAAI,CAAC,CAAC;;IAEzB,MAAMC,YAAY,GAAGpM,cAAc,CAACI,MAAM,CAACsB,MAAM,CAC/CuJ,KAAK,IAAIP,GAAG,GAAGO,KAAK,CAACT,SAAS,GAAG2B,UACnC,CAAC;IAED,MAAME,eAAe,GAAGrM,cAAc,CAACG,SAAS,CAACuB,MAAM,CACrD4K,GAAG,IAAI5B,GAAG,GAAG4B,GAAG,CAAC9B,SAAS,GAAG2B,UAC/B,CAAC;IAED,MAAMH,YAAY,GAAGI,YAAY,CAAClJ,MAAM,IAAIiJ,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;IAChE,MAAMF,iBAAiB,GAAGI,eAAe,CAACvC,MAAM,CAAC,CAACC,GAAG,EAAEuC,GAAG,KAAKvC,GAAG,GAAGuC,GAAG,CAACxB,QAAQ,EAAE,CAAC,CAAC,GAAGuB,eAAe,CAACnJ,MAAM;IAE9G,OAAO;MAAE8I,YAAY;MAAEC;IAAkB,CAAC;EAC5C,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACK,OAAO,EAAEC,OAAO,KAAK;IAC7CvM,iBAAiB,CAAC0D,IAAI,KAAK;MACzB,GAAGA,IAAI;MACPlD,SAAS,EAAE,IAAI;MACfJ,WAAW,EAAE;QACXC,CAAC,EAAEiM,OAAO;QACVhM,CAAC,EAAEiM,OAAO;QACVhM,KAAK,EAAE,GAAG,CAAC;MACb;IACF,CAAC,CAAC,CAAC;;IAEH;IACAsC,UAAU,CAAC,MAAM;MACf7C,iBAAiB,CAAC0D,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPlD,SAAS,EAAE,KAAK;QAChBJ,WAAW,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE;MACtC,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMiM,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,GAAG,MAAM,KAAK;IACnD,IAAIhP,YAAY,EAAE;MAChB,IAAI;QACF,IAAIgP,MAAM,KAAK,MAAM,EAAE;UACrB;UACA,MAAMC,GAAG,GAAGvF,GAAG,CAACC,eAAe,CAAC3J,YAAY,CAAC;UAC7C,MAAMkP,CAAC,GAAGxB,QAAQ,CAACyB,aAAa,CAAC,GAAG,CAAC;UACrCD,CAAC,CAACE,IAAI,GAAGH,GAAG;UACZC,CAAC,CAACG,QAAQ,GAAG,aAAa,IAAItC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC,CAAC/K,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACgL,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UACzFL,CAAC,CAAC3B,KAAK,CAAC,CAAC;UACT7D,GAAG,CAAC8F,eAAe,CAACP,GAAG,CAAC;UACxBxO,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,MAAM,IAAIuO,MAAM,KAAK,KAAK,EAAE;UAC3B3O,SAAS,CAAC,sBAAsB,CAAC;;UAEjC;UACA,MAAMoP,WAAW,GAAG,MAAMzP,YAAY,CAACyP,WAAW,CAAC,CAAC;UACpD,MAAMC,UAAU,GAAG,IAAI1D,UAAU,CAACyD,WAAW,CAAC;;UAE9C;UACA,MAAME,QAAQ,GAAG,aAAa,IAAI5C,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC,CAAC/K,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACgL,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UAC7F,MAAMK,SAAS,GAAG,MAAM/K,MAAM,CAACC,WAAW,CAAC+K,aAAa,CAACH,UAAU,EAAEC,QAAQ,CAAC;UAE9E,IAAIC,SAAS,EAAE;YACbvP,SAAS,CAAC,yBAAyB,CAAC;YACpCI,oBAAoB,CAAC,KAAK,CAAC;UAC7B;QACF;MACF,CAAC,CAAC,OAAOgE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDpE,SAAS,CAAC,0BAA0B,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMyP,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAI9M,QAAQ,CAAC+C,OAAO,IAAI/C,QAAQ,CAAC+C,OAAO,CAAC6D,GAAG,EAAE;MAC5CF,GAAG,CAAC8F,eAAe,CAACxM,QAAQ,CAAC+C,OAAO,CAAC6D,GAAG,CAAC;MACzC5G,QAAQ,CAAC+C,OAAO,CAAC6D,GAAG,GAAG,EAAE;IAC3B;IACA3J,eAAe,CAAC,IAAI,CAAC;IACrBI,SAAS,CAAC,iBAAiB,CAAC;EAC9B,CAAC;EAED,oBACEhB,OAAA;IAAK0Q,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB3Q,OAAA;MAAQ0Q,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACxB3Q,OAAA;QAAA2Q,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB/Q,OAAA;QAAK0Q,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3Q,OAAA;UACE0Q,SAAS,EAAC,mBAAmB;UAC7BM,OAAO,EAAE5M,gBAAiB;UAAAuM,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/Q,OAAA;UACE0Q,SAAS,EAAC,mBAAmB;UAC7BM,OAAO,EAAE3D,YAAa;UACtB4D,QAAQ,EAAE5Q,WAAY;UAAAsQ,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRlQ,WAAW,CAAC8E,MAAM,KAAK,QAAQ,iBAC9B3F,OAAA;UAAQ0Q,SAAS,EAAC,iBAAiB;UAACM,OAAO,EAAEhL,kBAAmB;UAAA2K,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAER3O,YAAY,iBACXpC,OAAA;MAAK0Q,SAAS,EAAC,eAAe;MAACM,OAAO,EAAE1D,aAAc;MAAAqD,QAAA,eACpD3Q,OAAA;QAAK0Q,SAAS,EAAC,eAAe;QAACM,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;QAAAR,QAAA,gBACjE3Q,OAAA;UAAK0Q,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3Q,OAAA;YAAA2Q,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B/Q,OAAA;YAAQ0Q,SAAS,EAAC,aAAa;YAACM,OAAO,EAAE1D,aAAc;YAAAqD,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAEN/Q,OAAA;UAAK0Q,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cAAA2Q,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd/Q,OAAA;cAAO0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC7B3Q,OAAA;gBACEiK,IAAI,EAAC,UAAU;gBACfmH,OAAO,EAAE,CAAA1O,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAElB,iBAAiB,KAAI,KAAM;gBAClD6P,QAAQ,EAAGH,CAAC,IAAKvO,eAAe,CAACiE,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPpF,iBAAiB,EAAE0P,CAAC,CAACI,MAAM,CAACF;gBAC9B,CAAC,CAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,sBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAEP,CAAArO,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAElB,iBAAiB,kBAC9BxB,OAAA;cAAK0Q,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3C3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjC/Q,OAAA;gBACEiN,KAAK,EAAE,CAAAvK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEb,kBAAkB,KAAI,SAAU;gBACrDwP,QAAQ,EAAGH,CAAC,IAAKvO,eAAe,CAACiE,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACP/E,kBAAkB,EAAEqP,CAAC,CAACI,MAAM,CAACrE;gBAC/B,CAAC,CAAC,CAAE;gBAAA0D,QAAA,EAEH/N,gBAAgB,CAACE,WAAW,CAACgC,GAAG,CAACF,MAAM,iBACtC5E,OAAA;kBAAwBiN,KAAK,EAAErI,MAAM,CAACG,EAAG;kBAAA4L,QAAA,EACtC/L,MAAM,CAACK;gBAAK,GADFL,MAAM,CAACG,EAAE;kBAAA6L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAED/Q,OAAA;cAAO0Q,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACtC3Q,OAAA;gBACEiK,IAAI,EAAC,UAAU;gBACfmH,OAAO,EAAE,KAAM;gBACfH,QAAQ,EAAE;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,wBAEF,eAAA/Q,OAAA;gBAAM0Q,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN/Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cAAA2Q,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd/Q,OAAA;cAAO0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC7B3Q,OAAA;gBACEiK,IAAI,EAAC,UAAU;gBACfmH,OAAO,EAAE,CAAA1O,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEjB,aAAa,KAAI,KAAM;gBAC9C4P,QAAQ,EAAGH,CAAC,IAAKvO,eAAe,CAACiE,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPnF,aAAa,EAAEyP,CAAC,CAACI,MAAM,CAACF;gBAC1B,CAAC,CAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,kBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAEP,CAAArO,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEjB,aAAa,kBAC1BzB,OAAA;cAAK0Q,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3C3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7B/Q,OAAA;gBACEiN,KAAK,EAAE,CAAAvK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEZ,cAAc,KAAI,SAAU;gBACjDuP,QAAQ,EAAGH,CAAC,IAAKvO,eAAe,CAACiE,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACP9E,cAAc,EAAEoP,CAAC,CAACI,MAAM,CAACrE;gBAC3B,CAAC,CAAC,CAAE;gBAAA0D,QAAA,EAEH/N,gBAAgB,CAACG,OAAO,CAAC+B,GAAG,CAACF,MAAM,iBAClC5E,OAAA;kBAAwBiN,KAAK,EAAErI,MAAM,CAACG,EAAG;kBAAA4L,QAAA,EACtC/L,MAAM,CAACK;gBAAK,GADFL,MAAM,CAACG,EAAE;kBAAA6L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAED/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B/Q,OAAA;gBACEiN,KAAK,EAAE,CAAAvK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEhB,UAAU,KAAI,WAAY;gBAC/C2P,QAAQ,EAAGH,CAAC,IAAKvO,eAAe,CAACiE,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPlF,UAAU,EAAEwP,CAAC,CAACI,MAAM,CAACrE;gBACvB,CAAC,CAAC,CAAE;gBAAA0D,QAAA,gBAEJ3Q,OAAA;kBAAQiN,KAAK,EAAC,UAAU;kBAAA0D,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD/Q,OAAA;kBAAQiN,KAAK,EAAC,WAAW;kBAAA0D,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD/Q,OAAA;kBAAQiN,KAAK,EAAC,WAAW;kBAAA0D,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD/Q,OAAA;kBAAQiN,KAAK,EAAC,WAAW;kBAAA0D,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B/Q,OAAA;gBACEiN,KAAK,EAAE,CAAAvK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEf,SAAS,KAAI,EAAG;gBACrC0P,QAAQ,EAAGH,CAAC,IAAKvO,eAAe,CAACiE,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPjF,SAAS,EAAE4P,QAAQ,CAACL,CAAC,CAACI,MAAM,CAACrE,KAAK;gBACpC,CAAC,CAAC,CAAE;gBAAA0D,QAAA,gBAEJ3Q,OAAA;kBAAQiN,KAAK,EAAE,EAAG;kBAAA0D,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC/Q,OAAA;kBAAQiN,KAAK,EAAE,EAAG;kBAAA0D,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB/Q,OAAA;gBACEiN,KAAK,EAAE,CAAAvK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEd,OAAO,KAAI,MAAO;gBACvCyP,QAAQ,EAAGH,CAAC,IAAKvO,eAAe,CAACiE,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPhF,OAAO,EAAEsP,CAAC,CAACI,MAAM,CAACrE;gBACpB,CAAC,CAAC,CAAE;gBAAA0D,QAAA,gBAEJ3Q,OAAA;kBAAQiN,KAAK,EAAC,OAAO;kBAAA0D,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnD/Q,OAAA;kBAAQiN,KAAK,EAAC,UAAU;kBAAA0D,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1C/Q,OAAA;kBAAQiN,KAAK,EAAC,MAAM;kBAAA0D,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cAAA2Q,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzC/Q,OAAA;gBACEiN,KAAK,EAAE,CAAAvK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAER,SAAS,KAAI,CAAE;gBACpCmP,QAAQ,EAAGH,CAAC,IAAKvO,eAAe,CAACiE,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACP1E,SAAS,EAAEqP,QAAQ,CAACL,CAAC,CAACI,MAAM,CAACrE,KAAK;gBACpC,CAAC,CAAC,CAAE;gBAAA0D,QAAA,gBAEJ3Q,OAAA;kBAAQiN,KAAK,EAAE,CAAE;kBAAA0D,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC/Q,OAAA;kBAAQiN,KAAK,EAAE,CAAE;kBAAA0D,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC/Q,OAAA;kBAAQiN,KAAK,EAAE,CAAE;kBAAA0D,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC/Q,OAAA;kBAAQiN,KAAK,EAAE,EAAG;kBAAA0D,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/Q,OAAA;UAAK0Q,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3Q,OAAA;YAAQ0Q,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAE1M,oBAAqB;YAAAqM,QAAA,EAAC;UAErE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/Q,OAAA;YAAK0Q,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC3Q,OAAA;cAAQ0Q,SAAS,EAAC,mBAAmB;cAACM,OAAO,EAAE1D,aAAc;cAAAqD,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/Q,OAAA;cAAQ0Q,SAAS,EAAC,iBAAiB;cAACM,OAAO,EAAEzD,YAAa;cAAAoD,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED/Q,OAAA;MAAM0Q,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACvB3Q,OAAA;QAAK0Q,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3Q,OAAA;UAAA2Q,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjC/Q,OAAA;UAAK0Q,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BpQ,OAAO,CAACuE,GAAG,CAAEuH,MAAM,iBAClBrM,OAAA;YAEE0Q,SAAS,EAAE,eAAe,CAAAjQ,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEsE,EAAE,MAAKsH,MAAM,CAACtH,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/EiM,OAAO,EAAEA,CAAA,KAAMtQ,iBAAiB,CAAC2L,MAAM,CAAE;YAAAsE,QAAA,gBAEzC3Q,OAAA;cACEuK,GAAG,EAAE8B,MAAM,CAACmF,SAAU;cACtBC,GAAG,EAAEpF,MAAM,CAAC/F,IAAK;cACjBoK,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACF/Q,OAAA;cAAK0Q,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEtE,MAAM,CAAC/F;YAAI;cAAAsK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAT3C1E,MAAM,CAACtH,EAAE;YAAA6L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/Q,OAAA;QAAK0Q,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BhQ,YAAY,gBACXX,OAAA;UAAK0Q,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3Q,OAAA;YACE0R,GAAG,EAAE/N,QAAS;YACd+M,SAAS,EAAC,eAAe;YACzBiB,QAAQ;YACRC,QAAQ,EAAE,KAAM;YAChBC,OAAO,EAAC,UAAU;YAClBC,KAAK;YACLC,WAAW;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACF/Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B3Q,OAAA;cAAA2Q,QAAA,GAAG,uCAAgC,EAAC5F,cAAc,CAAC9J,iBAAiB,CAAC;YAAA;cAAA2P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN/Q,OAAA;UAAK0Q,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3Q,OAAA;YAAK0Q,SAAS,EAAE,cAAcrQ,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;YAAAsQ,QAAA,EAC5DtQ,WAAW,gBACVL,OAAA;cAAK0Q,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B3Q,OAAA;gBAAK0Q,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC3Q,OAAA;kBAAM0Q,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,aAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN/Q,OAAA;gBAAK0Q,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAChC5F,cAAc,CAAC9J,iBAAiB;cAAC;gBAAA2P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACN/Q,OAAA;gBAAK0Q,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BlQ,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6F;cAAI;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACN/Q,OAAA;gBAAK0Q,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GACrCtP,iBAAiB,CAACK,UAAU,EAAC,UAAG,EAACL,iBAAiB,CAACM,SAAS,EAAC,aAAM,EAACN,iBAAiB,CAACO,OAAO;cAAA;gBAAAgP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC,EAGL,CAAC1P,iBAAiB,CAACG,iBAAiB,IAAIH,iBAAiB,CAACE,kBAAkB,kBAC3EvB,OAAA;gBAAK0Q,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC1BtP,iBAAiB,CAACG,iBAAiB,iBAClCxB,OAAA;kBAAK0Q,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B3Q,OAAA;oBAAM0Q,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC/Q,OAAA;oBAAK0Q,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1B3Q,OAAA;sBACE0Q,SAAS,EAAC,iBAAiB;sBAC3BsB,KAAK,EAAE;wBAAEnL,KAAK,EAAE,GAAGvE,WAAW,CAACE,UAAU,GAAG,GAAG;sBAAI;oBAAE;sBAAAoO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EACA1P,iBAAiB,CAACE,kBAAkB,iBACnCvB,OAAA;kBAAK0Q,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B3Q,OAAA;oBAAM0Q,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC/Q,OAAA;oBAAK0Q,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1B3Q,OAAA;sBACE0Q,SAAS,EAAC,iBAAiB;sBAC3BsB,KAAK,EAAE;wBAAEnL,KAAK,EAAE,GAAGvE,WAAW,CAACG,MAAM,GAAG,GAAG;sBAAI;oBAAE;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAK0Q,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE5P;cAAM;gBAAA6P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1CtQ,cAAc,iBACbT,OAAA;gBAAK0Q,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,mBAAiB,EAAClQ,cAAc,CAAC6F,IAAI;cAAA;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL/O,YAAY,IAAI3B,WAAW,iBAC1BL,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cACE0R,GAAG,EAAE9N,SAAU;cACf8M,SAAS,EAAC,cAAc;cACxBkB,QAAQ;cACRE,KAAK;cACLC,WAAW;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACF/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/Q,OAAA;QAAK0Q,SAAS,EAAC,UAAU;QAAAC,QAAA,GACtB,CAACtQ,WAAW,IAAI,CAACM,YAAY,iBAC5BX,OAAA;UACE0Q,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAExK,cAAe;UACxByK,QAAQ,EAAE,CAACxQ,cAAc,IAAII,WAAW,CAAC8E,MAAM,KAAK,QAAS;UAAAgL,QAAA,EAC9D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEA1Q,WAAW,iBACVL,OAAA;UAAQ0Q,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAEpG,aAAc;UAAA+F,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEApQ,YAAY,IAAI,CAACN,WAAW,iBAC3BL,OAAA,CAAAE,SAAA;UAAAyQ,QAAA,GACG,CAACxP,iBAAiB,gBACjBnB,OAAA;YACE0Q,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAM5P,oBAAoB,CAAC,IAAI,CAAE;YAAAuP,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET/Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cAAA2Q,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvB/Q,OAAA;cACE0Q,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAMtB,iBAAiB,CAAC,MAAM,CAAE;cAAAiB,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/Q,OAAA;cACE0Q,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAMtB,iBAAiB,CAAC,KAAK,CAAE;cAAAiB,QAAA,EACzC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/Q,OAAA;cACE0Q,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAEA,CAAA,KAAM5P,oBAAoB,CAAC,KAAK,CAAE;cAAAuP,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACD/Q,OAAA;YAAQ0Q,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAEP,cAAe;YAAAE,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,eAED/Q,OAAA;UAAQ0Q,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAE3M,WAAY;UAAAsM,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC3Q,EAAA,CAtgCQD,GAAG;AAAA8R,EAAA,GAAH9R,GAAG;AAwgCZ,eAAeA,GAAG;AAAC,IAAA8R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}