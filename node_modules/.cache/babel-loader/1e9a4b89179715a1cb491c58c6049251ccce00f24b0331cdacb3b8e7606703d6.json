{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n  const videoRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    try {\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Get screen stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: 1280,\n            maxWidth: 1920,\n            minHeight: 720,\n            maxHeight: 1080,\n            minFrameRate: 30\n          }\n        }\n      });\n\n      // Get microphone stream\n      let audioStream = null;\n      try {\n        audioStream = await navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          },\n          video: false\n        });\n      } catch (audioError) {\n        console.warn('Microphone access denied, recording video only');\n      }\n\n      // Combine streams\n      let combinedStream = stream;\n      if (audioStream) {\n        const audioTracks = audioStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n      }\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType: 'video/webm;codecs=vp9,opus'\n      });\n      const chunks = [];\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, {\n          type: 'video/webm'\n        });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Show preview\n        if (videoRef.current) {\n          videoRef.current.src = URL.createObjectURL(blob);\n        }\n      };\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to buffer\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const buffer = Buffer.from(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(buffer, filename);\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  const clearRecording = () => {\n    setRecordedBlob(null);\n    if (videoRef.current) {\n      videoRef.current.src = '';\n    }\n    setStatus('Ready to record');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Screen Recorder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-info\",\n        children: permissions.screen === 'denied' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: requestPermissions,\n          children: \"Grant Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"source-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select Screen or Window:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sources-grid\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `source-item ${(selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.id) === source.id ? 'selected' : ''}`,\n            onClick: () => setSelectedSource(source),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: source.thumbnail,\n              alt: source.name,\n              className: \"source-thumbnail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"source-name\",\n              children: source.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, source.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-area\",\n        children: recordedBlob ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: videoRef,\n            className: \"preview-video\",\n            controls: true,\n            autoPlay: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recording-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Recording completed \\u2022 Duration: \", formatDuration(recordingDuration)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-display\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status-box ${isRecording ? 'recording' : ''}`,\n            children: isRecording ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"recording-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"recording-dot\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this), \"RECORDING\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-duration\",\n                children: formatDuration(recordingDuration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-source\",\n                children: selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ready-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-text\",\n                children: status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), selectedSource && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-source\",\n                children: [\"Ready to record: \", selectedSource.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        children: [!isRecording && !recordedBlob && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: startRecording,\n          disabled: !selectedSource || permissions.screen === 'denied',\n          children: \"Start Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), isRecording && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger\",\n          onClick: stopRecording,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this), recordedBlob && !isRecording && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [!showExportOptions ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowExportOptions(true),\n            children: \"Download Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"export-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Choose format:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('webm'),\n              children: \"Download WebM (Fast)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('mp4'),\n              children: \"Download MP4 (Compatible)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => setShowExportOptions(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearRecording,\n            children: \"New Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: loadSources,\n          children: \"Refresh Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn\",\n          style: {\n            background: 'purple',\n            color: 'white'\n          },\n          onClick: () => alert('TEST: Code changes are working!'),\n          children: \"TEST BUTTON\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"ACRUjp1XvsPuH9G9WrKaBeoDrZM=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isRecording", "setIsRecording", "sources", "setSources", "selectedSource", "setSelectedSource", "recordedBlob", "setRecordedBlob", "permissions", "setPermissions", "status", "setStatus", "recordingDuration", "setRecordingDuration", "showExportOptions", "setShowExportOptions", "videoRef", "mediaRecorderRef", "streamRef", "durationIntervalRef", "checkPermissions", "loadSources", "perms", "window", "electronAPI", "requestPermissions", "granted", "getSources", "length", "screen", "find", "s", "name", "includes", "startRecording", "current", "setInterval", "prev", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "video", "mandatory", "chromeMediaSource", "chromeMediaSourceId", "id", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "minFrameRate", "audioStream", "echoCancellation", "noiseSuppression", "autoGainControl", "audioError", "console", "warn", "combinedStream", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "addTrack", "mediaRecorder", "MediaRecorder", "mimeType", "chunks", "ondataavailable", "event", "data", "size", "push", "onstop", "blob", "Blob", "type", "src", "URL", "createObjectURL", "start", "error", "clearInterval", "stopRecording", "stop", "getTracks", "formatDuration", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "downloadRecording", "format", "url", "a", "document", "createElement", "href", "download", "Date", "toISOString", "slice", "replace", "click", "revokeObjectURL", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "from", "filename", "savedPath", "saveRecording", "clearRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "source", "thumbnail", "alt", "ref", "controls", "autoPlay", "disabled", "style", "background", "color", "alert", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './index.css';\n\nfunction App() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n  \n  const videoRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  \n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  \n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  \n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  \n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  \n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    \n    try {\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n      \n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n      \n      // Get screen stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: 1280,\n            maxWidth: 1920,\n            minHeight: 720,\n            maxHeight: 1080,\n            minFrameRate: 30\n          }\n        }\n      });\n      \n      // Get microphone stream\n      let audioStream = null;\n      try {\n        audioStream = await navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          },\n          video: false\n        });\n      } catch (audioError) {\n        console.warn('Microphone access denied, recording video only');\n      }\n      \n      // Combine streams\n      let combinedStream = stream;\n      if (audioStream) {\n        const audioTracks = audioStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n      }\n      \n      streamRef.current = combinedStream;\n      \n      // Set up MediaRecorder\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType: 'video/webm;codecs=vp9,opus'\n      });\n      \n      const chunks = [];\n      \n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      \n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, { type: 'video/webm' });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n        \n        // Show preview\n        if (videoRef.current) {\n          videoRef.current.src = URL.createObjectURL(blob);\n        }\n      };\n      \n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n      \n      setIsRecording(true);\n      setStatus('Recording in progress');\n      \n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  \n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      \n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n      \n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n      \n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  \n  const formatDuration = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n          \n          // Convert blob to buffer\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const buffer = Buffer.from(arrayBuffer);\n          \n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(buffer, filename);\n          \n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  \n  const clearRecording = () => {\n    setRecordedBlob(null);\n    if (videoRef.current) {\n      videoRef.current.src = '';\n    }\n    setStatus('Ready to record');\n  };\n  \n  return (\n    <div className=\"app\">\n      <header className=\"header\">\n        <h1>Screen Recorder</h1>\n        <div className=\"status-info\">\n          {permissions.screen === 'denied' && (\n            <button className=\"btn btn-secondary\" onClick={requestPermissions}>\n              Grant Permissions\n            </button>\n          )}\n        </div>\n      </header>\n      \n      <main className=\"content\">\n        <div className=\"source-selector\">\n          <h3>Select Screen or Window:</h3>\n          <div className=\"sources-grid\">\n            {sources.map((source) => (\n              <div\n                key={source.id}\n                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}\n                onClick={() => setSelectedSource(source)}\n              >\n                <img\n                  src={source.thumbnail}\n                  alt={source.name}\n                  className=\"source-thumbnail\"\n                />\n                <div className=\"source-name\">{source.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"recording-area\">\n          {recordedBlob ? (\n            <div>\n              <video\n                ref={videoRef}\n                className=\"preview-video\"\n                controls\n                autoPlay={false}\n              />\n              <div className=\"recording-info\">\n                <p>Recording completed • Duration: {formatDuration(recordingDuration)}</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"status-display\">\n              <div className={`status-box ${isRecording ? 'recording' : ''}`}>\n                {isRecording ? (\n                  <div className=\"recording-status\">\n                    <div className=\"recording-indicator\">\n                      <span className=\"recording-dot\"></span>\n                      RECORDING\n                    </div>\n                    <div className=\"recording-duration\">\n                      {formatDuration(recordingDuration)}\n                    </div>\n                    <div className=\"recording-source\">\n                      {selectedSource?.name}\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"ready-status\">\n                    <div className=\"status-text\">{status}</div>\n                    {selectedSource && (\n                      <div className=\"selected-source\">Ready to record: {selectedSource.name}</div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"controls\">\n          {!isRecording && !recordedBlob && (\n            <button\n              className=\"btn btn-primary\"\n              onClick={startRecording}\n              disabled={!selectedSource || permissions.screen === 'denied'}\n            >\n              Start Recording\n            </button>\n          )}\n          \n          {isRecording && (\n            <button className=\"btn btn-danger\" onClick={stopRecording}>\n              Stop Recording\n            </button>\n          )}\n          \n          {recordedBlob && !isRecording && (\n            <>\n              {!showExportOptions ? (\n                <button \n                  className=\"btn btn-primary\" \n                  onClick={() => setShowExportOptions(true)}\n                >\n                  Download Recording\n                </button>\n              ) : (\n                <div className=\"export-options\">\n                  <h4>Choose format:</h4>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('webm')}\n                  >\n                    Download WebM (Fast)\n                  </button>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('mp4')}\n                  >\n                    Download MP4 (Compatible)\n                  </button>\n                  <button \n                    className=\"btn btn-secondary\" \n                    onClick={() => setShowExportOptions(false)}\n                  >\n                    Cancel\n                  </button>\n                </div>\n              )}\n              <button className=\"btn btn-secondary\" onClick={clearRecording}>\n                New Recording\n              </button>\n            </>\n          )}\n          \n          <button className=\"btn btn-secondary\" onClick={loadSources}>\n            Refresh Sources\n          </button>\n          \n          <button \n            className=\"btn\" \n            style={{background: 'purple', color: 'white'}} \n            onClick={() => alert('TEST: Code changes are working!')}\n          >\n            TEST BUTTON\n          </button>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,iBAAiB,CAAC;EACvD,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMyB,QAAQ,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMyB,gBAAgB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM0B,SAAS,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM2B,mBAAmB,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd2B,gBAAgB,CAAC,CAAC;IAClBC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAME,KAAK,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACJ,gBAAgB,CAAC,CAAC;IACzDX,cAAc,CAACa,KAAK,CAAC;EACvB,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,OAAO,GAAG,MAAMH,MAAM,CAACC,WAAW,CAACC,kBAAkB,CAAC,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACX,MAAMN,gBAAgB,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMnB,OAAO,GAAG,MAAMqB,MAAM,CAACC,WAAW,CAACG,UAAU,CAAC,CAAC;IACrDxB,UAAU,CAACD,OAAO,CAAC;IACnB,IAAIA,OAAO,CAAC0B,MAAM,GAAG,CAAC,IAAI,CAACxB,cAAc,EAAE;MACzC;MACA,MAAMyB,MAAM,GAAG3B,OAAO,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;MACxF5B,iBAAiB,CAACwB,MAAM,IAAI3B,OAAO,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMgC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAC9B,cAAc,EAAE;MACnBO,SAAS,CAAC,4CAA4C,CAAC;MACvD;IACF;IAEA,IAAI;MACFA,SAAS,CAAC,uBAAuB,CAAC;MAClCE,oBAAoB,CAAC,CAAC,CAAC;;MAEvB;MACAM,mBAAmB,CAACgB,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC9CvB,oBAAoB,CAACwB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;UACLC,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAE1C,cAAc,CAAC2C,EAAE;YACtCC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,IAAI;YACfC,YAAY,EAAE;UAChB;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIC,WAAW,GAAG,IAAI;MACtB,IAAI;QACFA,WAAW,GAAG,MAAMd,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACtDC,KAAK,EAAE;YACLY,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE;UACnB,CAAC;UACDb,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOc,UAAU,EAAE;QACnBC,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;MAChE;;MAEA;MACA,IAAIC,cAAc,GAAGtB,MAAM;MAC3B,IAAIe,WAAW,EAAE;QACf,MAAMQ,WAAW,GAAGR,WAAW,CAACS,cAAc,CAAC,CAAC;QAChDD,WAAW,CAACE,OAAO,CAACC,KAAK,IAAIJ,cAAc,CAACK,QAAQ,CAACD,KAAK,CAAC,CAAC;MAC9D;MAEA9C,SAAS,CAACiB,OAAO,GAAGyB,cAAc;;MAElC;MACA,MAAMM,aAAa,GAAG,IAAIC,aAAa,CAACP,cAAc,EAAE;QACtDQ,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG,EAAE;MAEjBH,aAAa,CAACI,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBJ,MAAM,CAACK,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzB;MACF,CAAC;MAEDN,aAAa,CAACS,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACR,MAAM,EAAE;UAAES,IAAI,EAAE;QAAa,CAAC,CAAC;QACrDvE,eAAe,CAACqE,IAAI,CAAC;QACrBjE,SAAS,CAAC,2CAA2C,CAAC;;QAEtD;QACA,IAAIK,QAAQ,CAACmB,OAAO,EAAE;UACpBnB,QAAQ,CAACmB,OAAO,CAAC4C,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;QAClD;MACF,CAAC;MAED3D,gBAAgB,CAACkB,OAAO,GAAG+B,aAAa;MACxCA,aAAa,CAACgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE1BjF,cAAc,CAAC,IAAI,CAAC;MACpBU,SAAS,CAAC,uBAAuB,CAAC;IAEpC,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDxE,SAAS,CAAC,sDAAsD,CAAC;MACjE,IAAIQ,mBAAmB,CAACgB,OAAO,EAAE;QAC/BiD,aAAa,CAACjE,mBAAmB,CAACgB,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMkD,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIpE,gBAAgB,CAACkB,OAAO,IAAInC,WAAW,EAAE;MAC3CiB,gBAAgB,CAACkB,OAAO,CAACmD,IAAI,CAAC,CAAC;;MAE/B;MACA,IAAInE,mBAAmB,CAACgB,OAAO,EAAE;QAC/BiD,aAAa,CAACjE,mBAAmB,CAACgB,OAAO,CAAC;MAC5C;;MAEA;MACA,IAAIjB,SAAS,CAACiB,OAAO,EAAE;QACrBjB,SAAS,CAACiB,OAAO,CAACoD,SAAS,CAAC,CAAC,CAACxB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACsB,IAAI,CAAC,CAAC,CAAC;MAC9D;MAEArF,cAAc,CAAC,KAAK,CAAC;MACrBU,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC;EAED,MAAM6E,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,GAAG,MAAM,KAAK;IACnD,IAAI3F,YAAY,EAAE;MAChB,IAAI;QACF,IAAI2F,MAAM,KAAK,MAAM,EAAE;UACrB;UACA,MAAMC,GAAG,GAAGlB,GAAG,CAACC,eAAe,CAAC3E,YAAY,CAAC;UAC7C,MAAM6F,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGJ,GAAG;UACZC,CAAC,CAACI,QAAQ,GAAG,aAAa,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UACzFR,CAAC,CAACS,KAAK,CAAC,CAAC;UACT5B,GAAG,CAAC6B,eAAe,CAACX,GAAG,CAAC;UACxBnF,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,MAAM,IAAIkF,MAAM,KAAK,KAAK,EAAE;UAC3BtF,SAAS,CAAC,sBAAsB,CAAC;;UAEjC;UACA,MAAMmG,WAAW,GAAG,MAAMxG,YAAY,CAACwG,WAAW,CAAC,CAAC;UACpD,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACH,WAAW,CAAC;;UAEvC;UACA,MAAMI,QAAQ,GAAG,aAAa,IAAIV,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UAC7F,MAAMQ,SAAS,GAAG,MAAM5F,MAAM,CAACC,WAAW,CAAC4F,aAAa,CAACL,MAAM,EAAEG,QAAQ,CAAC;UAE1E,IAAIC,SAAS,EAAE;YACbxG,SAAS,CAAC,yBAAyB,CAAC;YACpCI,oBAAoB,CAAC,KAAK,CAAC;UAC7B;QACF;MACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDxE,SAAS,CAAC,0BAA0B,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAM0G,cAAc,GAAGA,CAAA,KAAM;IAC3B9G,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIS,QAAQ,CAACmB,OAAO,EAAE;MACpBnB,QAAQ,CAACmB,OAAO,CAAC4C,GAAG,GAAG,EAAE;IAC3B;IACApE,SAAS,CAAC,iBAAiB,CAAC;EAC9B,CAAC;EAED,oBACEhB,OAAA;IAAK2H,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB5H,OAAA;MAAQ2H,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACxB5H,OAAA;QAAA4H,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBhI,OAAA;QAAK2H,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB/G,WAAW,CAACqB,MAAM,KAAK,QAAQ,iBAC9BlC,OAAA;UAAQ2H,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAEnG,kBAAmB;UAAA8F,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEThI,OAAA;MAAM2H,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACvB5H,OAAA;QAAK2H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B5H,OAAA;UAAA4H,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjChI,OAAA;UAAK2H,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BrH,OAAO,CAAC2H,GAAG,CAAEC,MAAM,iBAClBnI,OAAA;YAEE2H,SAAS,EAAE,eAAe,CAAAlH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C,EAAE,MAAK+E,MAAM,CAAC/E,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/E6E,OAAO,EAAEA,CAAA,KAAMvH,iBAAiB,CAACyH,MAAM,CAAE;YAAAP,QAAA,gBAEzC5H,OAAA;cACEoF,GAAG,EAAE+C,MAAM,CAACC,SAAU;cACtBC,GAAG,EAAEF,MAAM,CAAC9F,IAAK;cACjBsF,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACFhI,OAAA;cAAK2H,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEO,MAAM,CAAC9F;YAAI;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAT3CG,MAAM,CAAC/E,EAAE;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhI,OAAA;QAAK2H,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BjH,YAAY,gBACXX,OAAA;UAAA4H,QAAA,gBACE5H,OAAA;YACEsI,GAAG,EAAEjH,QAAS;YACdsG,SAAS,EAAC,eAAe;YACzBY,QAAQ;YACRC,QAAQ,EAAE;UAAM;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFhI,OAAA;YAAK2H,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B5H,OAAA;cAAA4H,QAAA,GAAG,uCAAgC,EAAC/B,cAAc,CAAC5E,iBAAiB,CAAC;YAAA;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENhI,OAAA;UAAK2H,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B5H,OAAA;YAAK2H,SAAS,EAAE,cAActH,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;YAAAuH,QAAA,EAC5DvH,WAAW,gBACVL,OAAA;cAAK2H,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B5H,OAAA;gBAAK2H,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC5H,OAAA;kBAAM2H,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,aAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhI,OAAA;gBAAK2H,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAChC/B,cAAc,CAAC5E,iBAAiB;cAAC;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNhI,OAAA;gBAAK2H,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BnH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4B;cAAI;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENhI,OAAA;cAAK2H,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5H,OAAA;gBAAK2H,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE7G;cAAM;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1CvH,cAAc,iBACbT,OAAA;gBAAK2H,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,mBAAiB,EAACnH,cAAc,CAAC4B,IAAI;cAAA;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENhI,OAAA;QAAK2H,SAAS,EAAC,UAAU;QAAAC,QAAA,GACtB,CAACvH,WAAW,IAAI,CAACM,YAAY,iBAC5BX,OAAA;UACE2H,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAE1F,cAAe;UACxBkG,QAAQ,EAAE,CAAChI,cAAc,IAAII,WAAW,CAACqB,MAAM,KAAK,QAAS;UAAA0F,QAAA,EAC9D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEA3H,WAAW,iBACVL,OAAA;UAAQ2H,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAEvC,aAAc;UAAAkC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEArH,YAAY,IAAI,CAACN,WAAW,iBAC3BL,OAAA,CAAAE,SAAA;UAAA0H,QAAA,GACG,CAACzG,iBAAiB,gBACjBnB,OAAA;YACE2H,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAM7G,oBAAoB,CAAC,IAAI,CAAE;YAAAwG,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAEThI,OAAA;YAAK2H,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B5H,OAAA;cAAA4H,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBhI,OAAA;cACE2H,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAAC,MAAM,CAAE;cAAAuB,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThI,OAAA;cACE2H,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAAC,KAAK,CAAE;cAAAuB,QAAA,EACzC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThI,OAAA;cACE2H,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAEA,CAAA,KAAM7G,oBAAoB,CAAC,KAAK,CAAE;cAAAwG,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDhI,OAAA;YAAQ2H,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAEP,cAAe;YAAAE,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,eAEDhI,OAAA;UAAQ2H,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAEvG,WAAY;UAAAkG,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEThI,OAAA;UACE2H,SAAS,EAAC,KAAK;UACfe,KAAK,EAAE;YAACC,UAAU,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAO,CAAE;UAC9CX,OAAO,EAAEA,CAAA,KAAMY,KAAK,CAAC,iCAAiC,CAAE;UAAAjB,QAAA,EACzD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC5H,EAAA,CA9VQD,GAAG;AAAA2I,EAAA,GAAH3I,GAAG;AAgWZ,eAAeA,GAAG;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}