{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const videoRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    try {\n      setStatus('Starting recording...');\n\n      // Get screen stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: 1280,\n            maxWidth: 1920,\n            minHeight: 720,\n            maxHeight: 1080,\n            minFrameRate: 30\n          }\n        }\n      });\n\n      // Get microphone stream\n      let audioStream = null;\n      try {\n        audioStream = await navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          },\n          video: false\n        });\n      } catch (audioError) {\n        console.warn('Microphone access denied, recording video only');\n      }\n\n      // Combine streams\n      let combinedStream = stream;\n      if (audioStream) {\n        const audioTracks = audioStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n      }\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType: 'video/webm;codecs=vp9,opus'\n      });\n      const chunks = [];\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, {\n          type: 'video/webm'\n        });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Show preview\n        if (videoRef.current) {\n          videoRef.current.src = URL.createObjectURL(blob);\n        }\n      };\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording...');\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n    }\n  };\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  const downloadRecording = async () => {\n    if (recordedBlob) {\n      try {\n        setStatus('Converting to MP4...');\n\n        // Convert blob to buffer\n        const arrayBuffer = await recordedBlob.arrayBuffer();\n        const buffer = Buffer.from(arrayBuffer);\n\n        // Save and convert via Electron\n        const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n        const savedPath = await window.electronAPI.saveRecording(buffer, filename);\n        if (savedPath) {\n          setStatus('Recording saved successfully!');\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  const clearRecording = () => {\n    setRecordedBlob(null);\n    if (videoRef.current) {\n      videoRef.current.src = '';\n    }\n    setStatus('Ready to record');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Screen Recorder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-info\",\n        children: permissions.screen === 'denied' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: requestPermissions,\n          children: \"Grant Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"source-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select Screen or Window:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sources-grid\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `source-item ${(selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.id) === source.id ? 'selected' : ''}`,\n            onClick: () => setSelectedSource(source),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: source.thumbnail,\n              alt: source.name,\n              className: \"source-thumbnail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"source-name\",\n              children: source.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, source.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-area\",\n        children: recordedBlob ? /*#__PURE__*/_jsxDEV(\"video\", {\n          ref: videoRef,\n          className: \"preview-video\",\n          controls: true,\n          autoPlay: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status ${isRecording ? 'recording' : ''}`,\n            children: status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        children: [!isRecording && !recordedBlob && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: startRecording,\n          disabled: !selectedSource || permissions.screen === 'denied',\n          children: \"Start Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), isRecording && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger\",\n          onClick: stopRecording,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), recordedBlob && !isRecording && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: downloadRecording,\n            children: \"Download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearRecording,\n            children: \"New Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: loadSources,\n          children: \"Refresh Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn\",\n          style: {\n            background: 'purple',\n            color: 'white'\n          },\n          onClick: () => alert('TEST: Code changes are working!'),\n          children: \"TEST BUTTON\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"4vZNiqnheqeytya9C2eQfl96zko=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isRecording", "setIsRecording", "sources", "setSources", "selectedSource", "setSelectedSource", "recordedBlob", "setRecordedBlob", "permissions", "setPermissions", "status", "setStatus", "videoRef", "mediaRecorderRef", "streamRef", "checkPermissions", "loadSources", "perms", "window", "electronAPI", "requestPermissions", "granted", "getSources", "length", "screen", "find", "s", "name", "includes", "startRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "video", "mandatory", "chromeMediaSource", "chromeMediaSourceId", "id", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "minFrameRate", "audioStream", "echoCancellation", "noiseSuppression", "autoGainControl", "audioError", "console", "warn", "combinedStream", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "addTrack", "current", "mediaRecorder", "MediaRecorder", "mimeType", "chunks", "ondataavailable", "event", "data", "size", "push", "onstop", "blob", "Blob", "type", "src", "URL", "createObjectURL", "start", "error", "stopRecording", "stop", "getTracks", "downloadRecording", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "from", "filename", "Date", "toISOString", "slice", "replace", "savedPath", "saveRecording", "clearRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "source", "thumbnail", "alt", "ref", "controls", "autoPlay", "disabled", "style", "background", "color", "alert", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './index.css';\n\nfunction App() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  \n  const videoRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  \n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  \n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  \n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  \n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  \n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    \n    try {\n      setStatus('Starting recording...');\n      \n      // Get screen stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: 1280,\n            maxWidth: 1920,\n            minHeight: 720,\n            maxHeight: 1080,\n            minFrameRate: 30\n          }\n        }\n      });\n      \n      // Get microphone stream\n      let audioStream = null;\n      try {\n        audioStream = await navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          },\n          video: false\n        });\n      } catch (audioError) {\n        console.warn('Microphone access denied, recording video only');\n      }\n      \n      // Combine streams\n      let combinedStream = stream;\n      if (audioStream) {\n        const audioTracks = audioStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n      }\n      \n      streamRef.current = combinedStream;\n      \n      // Set up MediaRecorder\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType: 'video/webm;codecs=vp9,opus'\n      });\n      \n      const chunks = [];\n      \n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      \n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, { type: 'video/webm' });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n        \n        // Show preview\n        if (videoRef.current) {\n          videoRef.current.src = URL.createObjectURL(blob);\n        }\n      };\n      \n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n      \n      setIsRecording(true);\n      setStatus('Recording...');\n      \n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n    }\n  };\n  \n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      \n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n      \n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  \n  const downloadRecording = async () => {\n    if (recordedBlob) {\n      try {\n        setStatus('Converting to MP4...');\n        \n        // Convert blob to buffer\n        const arrayBuffer = await recordedBlob.arrayBuffer();\n        const buffer = Buffer.from(arrayBuffer);\n        \n        // Save and convert via Electron\n        const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n        const savedPath = await window.electronAPI.saveRecording(buffer, filename);\n        \n        if (savedPath) {\n          setStatus('Recording saved successfully!');\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  \n  const clearRecording = () => {\n    setRecordedBlob(null);\n    if (videoRef.current) {\n      videoRef.current.src = '';\n    }\n    setStatus('Ready to record');\n  };\n  \n  return (\n    <div className=\"app\">\n      <header className=\"header\">\n        <h1>Screen Recorder</h1>\n        <div className=\"status-info\">\n          {permissions.screen === 'denied' && (\n            <button className=\"btn btn-secondary\" onClick={requestPermissions}>\n              Grant Permissions\n            </button>\n          )}\n        </div>\n      </header>\n      \n      <main className=\"content\">\n        <div className=\"source-selector\">\n          <h3>Select Screen or Window:</h3>\n          <div className=\"sources-grid\">\n            {sources.map((source) => (\n              <div\n                key={source.id}\n                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}\n                onClick={() => setSelectedSource(source)}\n              >\n                <img\n                  src={source.thumbnail}\n                  alt={source.name}\n                  className=\"source-thumbnail\"\n                />\n                <div className=\"source-name\">{source.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"recording-area\">\n          {recordedBlob ? (\n            <video\n              ref={videoRef}\n              className=\"preview-video\"\n              controls\n              autoPlay={false}\n            />\n          ) : (\n            <div>\n              <div className={`status ${isRecording ? 'recording' : ''}`}>\n                {status}\n              </div>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"controls\">\n          {!isRecording && !recordedBlob && (\n            <button\n              className=\"btn btn-primary\"\n              onClick={startRecording}\n              disabled={!selectedSource || permissions.screen === 'denied'}\n            >\n              Start Recording\n            </button>\n          )}\n          \n          {isRecording && (\n            <button className=\"btn btn-danger\" onClick={stopRecording}>\n              Stop Recording\n            </button>\n          )}\n          \n          {recordedBlob && !isRecording && (\n            <>\n              <button className=\"btn btn-primary\" onClick={downloadRecording}>\n                Download\n              </button>\n              <button className=\"btn btn-secondary\" onClick={clearRecording}>\n                New Recording\n              </button>\n            </>\n          )}\n          \n          <button className=\"btn btn-secondary\" onClick={loadSources}>\n            Refresh Sources\n          </button>\n          \n          <button \n            className=\"btn\" \n            style={{background: 'purple', color: 'white'}} \n            onClick={() => alert('TEST: Code changes are working!')}\n          >\n            TEST BUTTON\n          </button>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,iBAAiB,CAAC;EAEvD,MAAMqB,QAAQ,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMqB,gBAAgB,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMsB,SAAS,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAE9BC,SAAS,CAAC,MAAM;IACdsB,gBAAgB,CAAC,CAAC;IAClBC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAME,KAAK,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACJ,gBAAgB,CAAC,CAAC;IACzDN,cAAc,CAACQ,KAAK,CAAC;EACvB,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,OAAO,GAAG,MAAMH,MAAM,CAACC,WAAW,CAACC,kBAAkB,CAAC,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACX,MAAMN,gBAAgB,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMd,OAAO,GAAG,MAAMgB,MAAM,CAACC,WAAW,CAACG,UAAU,CAAC,CAAC;IACrDnB,UAAU,CAACD,OAAO,CAAC;IACnB,IAAIA,OAAO,CAACqB,MAAM,GAAG,CAAC,IAAI,CAACnB,cAAc,EAAE;MACzC;MACA,MAAMoB,MAAM,GAAGtB,OAAO,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;MACxFvB,iBAAiB,CAACmB,MAAM,IAAItB,OAAO,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAM2B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACzB,cAAc,EAAE;MACnBO,SAAS,CAAC,4CAA4C,CAAC;MACvD;IACF;IAEA,IAAI;MACFA,SAAS,CAAC,uBAAuB,CAAC;;MAElC;MACA,MAAMmB,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;UACLC,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAElC,cAAc,CAACmC,EAAE;YACtCC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,IAAI;YACfC,YAAY,EAAE;UAChB;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIC,WAAW,GAAG,IAAI;MACtB,IAAI;QACFA,WAAW,GAAG,MAAMd,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACtDC,KAAK,EAAE;YACLY,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE;UACnB,CAAC;UACDb,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOc,UAAU,EAAE;QACnBC,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;MAChE;;MAEA;MACA,IAAIC,cAAc,GAAGtB,MAAM;MAC3B,IAAIe,WAAW,EAAE;QACf,MAAMQ,WAAW,GAAGR,WAAW,CAACS,cAAc,CAAC,CAAC;QAChDD,WAAW,CAACE,OAAO,CAACC,KAAK,IAAIJ,cAAc,CAACK,QAAQ,CAACD,KAAK,CAAC,CAAC;MAC9D;MAEA1C,SAAS,CAAC4C,OAAO,GAAGN,cAAc;;MAElC;MACA,MAAMO,aAAa,GAAG,IAAIC,aAAa,CAACR,cAAc,EAAE;QACtDS,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG,EAAE;MAEjBH,aAAa,CAACI,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBJ,MAAM,CAACK,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzB;MACF,CAAC;MAEDN,aAAa,CAACS,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACR,MAAM,EAAE;UAAES,IAAI,EAAE;QAAa,CAAC,CAAC;QACrDhE,eAAe,CAAC8D,IAAI,CAAC;QACrB1D,SAAS,CAAC,2CAA2C,CAAC;;QAEtD;QACA,IAAIC,QAAQ,CAAC8C,OAAO,EAAE;UACpB9C,QAAQ,CAAC8C,OAAO,CAACc,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;QAClD;MACF,CAAC;MAEDxD,gBAAgB,CAAC6C,OAAO,GAAGC,aAAa;MACxCA,aAAa,CAACgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE1B1E,cAAc,CAAC,IAAI,CAAC;MACpBU,SAAS,CAAC,cAAc,CAAC;IAE3B,CAAC,CAAC,OAAOiE,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDjE,SAAS,CAAC,sDAAsD,CAAC;IACnE;EACF,CAAC;EAED,MAAMkE,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIhE,gBAAgB,CAAC6C,OAAO,IAAI1D,WAAW,EAAE;MAC3Ca,gBAAgB,CAAC6C,OAAO,CAACoB,IAAI,CAAC,CAAC;;MAE/B;MACA,IAAIhE,SAAS,CAAC4C,OAAO,EAAE;QACrB5C,SAAS,CAAC4C,OAAO,CAACqB,SAAS,CAAC,CAAC,CAACxB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACsB,IAAI,CAAC,CAAC,CAAC;MAC9D;MAEA7E,cAAc,CAAC,KAAK,CAAC;MACrBU,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC;EAED,MAAMqE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI1E,YAAY,EAAE;MAChB,IAAI;QACFK,SAAS,CAAC,sBAAsB,CAAC;;QAEjC;QACA,MAAMsE,WAAW,GAAG,MAAM3E,YAAY,CAAC2E,WAAW,CAAC,CAAC;QACpD,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACH,WAAW,CAAC;;QAEvC;QACA,MAAMI,QAAQ,GAAG,aAAa,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;QAC7F,MAAMC,SAAS,GAAG,MAAMxE,MAAM,CAACC,WAAW,CAACwE,aAAa,CAACT,MAAM,EAAEG,QAAQ,CAAC;QAE1E,IAAIK,SAAS,EAAE;UACb/E,SAAS,CAAC,+BAA+B,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOiE,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDjE,SAAS,CAAC,0BAA0B,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMiF,cAAc,GAAGA,CAAA,KAAM;IAC3BrF,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIK,QAAQ,CAAC8C,OAAO,EAAE;MACpB9C,QAAQ,CAAC8C,OAAO,CAACc,GAAG,GAAG,EAAE;IAC3B;IACA7D,SAAS,CAAC,iBAAiB,CAAC;EAC9B,CAAC;EAED,oBACEhB,OAAA;IAAKkG,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBnG,OAAA;MAAQkG,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACxBnG,OAAA;QAAAmG,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBvG,OAAA;QAAKkG,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBtF,WAAW,CAACgB,MAAM,KAAK,QAAQ,iBAC9B7B,OAAA;UAAQkG,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAE/E,kBAAmB;UAAA0E,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETvG,OAAA;MAAMkG,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACvBnG,OAAA;QAAKkG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnG,OAAA;UAAAmG,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCvG,OAAA;UAAKkG,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B5F,OAAO,CAACkG,GAAG,CAAEC,MAAM,iBAClB1G,OAAA;YAEEkG,SAAS,EAAE,eAAe,CAAAzF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmC,EAAE,MAAK8D,MAAM,CAAC9D,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/E4D,OAAO,EAAEA,CAAA,KAAM9F,iBAAiB,CAACgG,MAAM,CAAE;YAAAP,QAAA,gBAEzCnG,OAAA;cACE6E,GAAG,EAAE6B,MAAM,CAACC,SAAU;cACtBC,GAAG,EAAEF,MAAM,CAAC1E,IAAK;cACjBkE,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACFvG,OAAA;cAAKkG,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEO,MAAM,CAAC1E;YAAI;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAT3CG,MAAM,CAAC9D,EAAE;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvG,OAAA;QAAKkG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BxF,YAAY,gBACXX,OAAA;UACE6G,GAAG,EAAE5F,QAAS;UACdiF,SAAS,EAAC,eAAe;UACzBY,QAAQ;UACRC,QAAQ,EAAE;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAEFvG,OAAA;UAAAmG,QAAA,eACEnG,OAAA;YAAKkG,SAAS,EAAE,UAAU7F,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;YAAA8F,QAAA,EACxDpF;UAAM;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENvG,OAAA;QAAKkG,SAAS,EAAC,UAAU;QAAAC,QAAA,GACtB,CAAC9F,WAAW,IAAI,CAACM,YAAY,iBAC5BX,OAAA;UACEkG,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEtE,cAAe;UACxB8E,QAAQ,EAAE,CAACvG,cAAc,IAAII,WAAW,CAACgB,MAAM,KAAK,QAAS;UAAAsE,QAAA,EAC9D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAlG,WAAW,iBACVL,OAAA;UAAQkG,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAEtB,aAAc;UAAAiB,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEA5F,YAAY,IAAI,CAACN,WAAW,iBAC3BL,OAAA,CAAAE,SAAA;UAAAiG,QAAA,gBACEnG,OAAA;YAAQkG,SAAS,EAAC,iBAAiB;YAACM,OAAO,EAAEnB,iBAAkB;YAAAc,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvG,OAAA;YAAQkG,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAEP,cAAe;YAAAE,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,eAEDvG,OAAA;UAAQkG,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAEnF,WAAY;UAAA8E,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvG,OAAA;UACEkG,SAAS,EAAC,KAAK;UACfe,KAAK,EAAE;YAACC,UAAU,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAO,CAAE;UAC9CX,OAAO,EAAEA,CAAA,KAAMY,KAAK,CAAC,iCAAiC,CAAE;UAAAjB,QAAA,EACzD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACnG,EAAA,CAvQQD,GAAG;AAAAkH,EAAA,GAAHlH,GAAG;AAyQZ,eAAeA,GAAG;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}