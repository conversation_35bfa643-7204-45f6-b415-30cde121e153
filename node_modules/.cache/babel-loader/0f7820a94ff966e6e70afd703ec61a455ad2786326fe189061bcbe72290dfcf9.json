{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n  const videoRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    try {\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Get screen stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: 1280,\n            maxWidth: 1920,\n            minHeight: 720,\n            maxHeight: 1080,\n            minFrameRate: 30\n          }\n        }\n      });\n\n      // Get microphone stream\n      let audioStream = null;\n      try {\n        audioStream = await navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          },\n          video: false\n        });\n      } catch (audioError) {\n        console.warn('Microphone access denied, recording video only');\n      }\n\n      // Combine streams\n      let combinedStream = stream;\n      if (audioStream) {\n        const audioTracks = audioStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n      }\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType: 'video/webm;codecs=vp9,opus'\n      });\n      const chunks = [];\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, {\n          type: 'video/webm'\n        });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Screen Recorder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-info\",\n        children: permissions.screen === 'denied' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: requestPermissions,\n          children: \"Grant Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"source-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select Screen or Window:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sources-grid\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `source-item ${(selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.id) === source.id ? 'selected' : ''}`,\n            onClick: () => setSelectedSource(source),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: source.thumbnail,\n              alt: source.name,\n              className: \"source-thumbnail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"source-name\",\n              children: source.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)]\n          }, source.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-area\",\n        children: recordedBlob ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: videoRef,\n            className: \"preview-video\",\n            controls: true,\n            autoPlay: false,\n            preload: \"metadata\",\n            muted: true,\n            playsInline: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recording-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Recording completed \\u2022 Duration: \", formatDuration(recordingDuration)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-display\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status-box ${isRecording ? 'recording' : ''}`,\n            children: isRecording ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"recording-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"recording-dot\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 23\n                }, this), \"RECORDING\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-duration\",\n                children: formatDuration(recordingDuration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-source\",\n                children: selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ready-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-text\",\n                children: status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 21\n              }, this), selectedSource && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-source\",\n                children: [\"Ready to record: \", selectedSource.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        children: [!isRecording && !recordedBlob && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: startRecording,\n          disabled: !selectedSource || permissions.screen === 'denied',\n          children: \"Start Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), isRecording && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger\",\n          onClick: stopRecording,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), recordedBlob && !isRecording && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [!showExportOptions ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowExportOptions(true),\n            children: \"Download Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"export-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Choose format:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('webm'),\n              children: \"Download WebM (Fast)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('mp4'),\n              children: \"Download MP4 (Compatible)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => setShowExportOptions(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearRecording,\n            children: \"New Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: loadSources,\n          children: \"Refresh Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn\",\n          style: {\n            background: 'purple',\n            color: 'white'\n          },\n          onClick: () => alert('TEST: Code changes are working!'),\n          children: \"TEST BUTTON\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"ACRUjp1XvsPuH9G9WrKaBeoDrZM=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isRecording", "setIsRecording", "sources", "setSources", "selectedSource", "setSelectedSource", "recordedBlob", "setRecordedBlob", "permissions", "setPermissions", "status", "setStatus", "recordingDuration", "setRecordingDuration", "showExportOptions", "setShowExportOptions", "videoRef", "mediaRecorderRef", "streamRef", "durationIntervalRef", "checkPermissions", "loadSources", "perms", "window", "electronAPI", "requestPermissions", "granted", "getSources", "length", "screen", "find", "s", "name", "includes", "startRecording", "current", "setInterval", "prev", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "video", "mandatory", "chromeMediaSource", "chromeMediaSourceId", "id", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "minFrameRate", "audioStream", "echoCancellation", "noiseSuppression", "autoGainControl", "audioError", "console", "warn", "combinedStream", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "addTrack", "mediaRecorder", "MediaRecorder", "mimeType", "chunks", "ondataavailable", "event", "data", "size", "push", "onstop", "blob", "Blob", "type", "setTimeout", "videoUrl", "URL", "createObjectURL", "src", "load", "start", "error", "clearInterval", "stopRecording", "stop", "getTracks", "formatDuration", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "downloadRecording", "format", "url", "a", "document", "createElement", "href", "download", "Date", "toISOString", "slice", "replace", "click", "revokeObjectURL", "arrayBuffer", "uint8Array", "Uint8Array", "filename", "savedPath", "saveRecording", "clearRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "source", "thumbnail", "alt", "ref", "controls", "autoPlay", "preload", "muted", "playsInline", "disabled", "style", "background", "color", "alert", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './index.css';\n\nfunction App() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n  \n  const videoRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  \n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  \n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  \n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  \n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  \n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    \n    try {\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n      \n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n      \n      // Get screen stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: 1280,\n            maxWidth: 1920,\n            minHeight: 720,\n            maxHeight: 1080,\n            minFrameRate: 30\n          }\n        }\n      });\n      \n      // Get microphone stream\n      let audioStream = null;\n      try {\n        audioStream = await navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          },\n          video: false\n        });\n      } catch (audioError) {\n        console.warn('Microphone access denied, recording video only');\n      }\n      \n      // Combine streams\n      let combinedStream = stream;\n      if (audioStream) {\n        const audioTracks = audioStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n      }\n      \n      streamRef.current = combinedStream;\n      \n      // Set up MediaRecorder\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType: 'video/webm;codecs=vp9,opus'\n      });\n      \n      const chunks = [];\n      \n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      \n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, { type: 'video/webm' });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n      \n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n      \n      setIsRecording(true);\n      setStatus('Recording in progress');\n      \n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  \n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      \n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n      \n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n      \n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  \n  const formatDuration = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  \n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  \n  return (\n    <div className=\"app\">\n      <header className=\"header\">\n        <h1>Screen Recorder</h1>\n        <div className=\"status-info\">\n          {permissions.screen === 'denied' && (\n            <button className=\"btn btn-secondary\" onClick={requestPermissions}>\n              Grant Permissions\n            </button>\n          )}\n        </div>\n      </header>\n      \n      <main className=\"content\">\n        <div className=\"source-selector\">\n          <h3>Select Screen or Window:</h3>\n          <div className=\"sources-grid\">\n            {sources.map((source) => (\n              <div\n                key={source.id}\n                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}\n                onClick={() => setSelectedSource(source)}\n              >\n                <img\n                  src={source.thumbnail}\n                  alt={source.name}\n                  className=\"source-thumbnail\"\n                />\n                <div className=\"source-name\">{source.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"recording-area\">\n          {recordedBlob ? (\n            <div>\n              <video\n                ref={videoRef}\n                className=\"preview-video\"\n                controls\n                autoPlay={false}\n                preload=\"metadata\"\n                muted\n                playsInline\n              />\n              <div className=\"recording-info\">\n                <p>Recording completed • Duration: {formatDuration(recordingDuration)}</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"status-display\">\n              <div className={`status-box ${isRecording ? 'recording' : ''}`}>\n                {isRecording ? (\n                  <div className=\"recording-status\">\n                    <div className=\"recording-indicator\">\n                      <span className=\"recording-dot\"></span>\n                      RECORDING\n                    </div>\n                    <div className=\"recording-duration\">\n                      {formatDuration(recordingDuration)}\n                    </div>\n                    <div className=\"recording-source\">\n                      {selectedSource?.name}\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"ready-status\">\n                    <div className=\"status-text\">{status}</div>\n                    {selectedSource && (\n                      <div className=\"selected-source\">Ready to record: {selectedSource.name}</div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"controls\">\n          {!isRecording && !recordedBlob && (\n            <button\n              className=\"btn btn-primary\"\n              onClick={startRecording}\n              disabled={!selectedSource || permissions.screen === 'denied'}\n            >\n              Start Recording\n            </button>\n          )}\n          \n          {isRecording && (\n            <button className=\"btn btn-danger\" onClick={stopRecording}>\n              Stop Recording\n            </button>\n          )}\n          \n          {recordedBlob && !isRecording && (\n            <>\n              {!showExportOptions ? (\n                <button \n                  className=\"btn btn-primary\" \n                  onClick={() => setShowExportOptions(true)}\n                >\n                  Download Recording\n                </button>\n              ) : (\n                <div className=\"export-options\">\n                  <h4>Choose format:</h4>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('webm')}\n                  >\n                    Download WebM (Fast)\n                  </button>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('mp4')}\n                  >\n                    Download MP4 (Compatible)\n                  </button>\n                  <button \n                    className=\"btn btn-secondary\" \n                    onClick={() => setShowExportOptions(false)}\n                  >\n                    Cancel\n                  </button>\n                </div>\n              )}\n              <button className=\"btn btn-secondary\" onClick={clearRecording}>\n                New Recording\n              </button>\n            </>\n          )}\n          \n          <button className=\"btn btn-secondary\" onClick={loadSources}>\n            Refresh Sources\n          </button>\n          \n          <button \n            className=\"btn\" \n            style={{background: 'purple', color: 'white'}} \n            onClick={() => alert('TEST: Code changes are working!')}\n          >\n            TEST BUTTON\n          </button>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,iBAAiB,CAAC;EACvD,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMyB,QAAQ,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMyB,gBAAgB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM0B,SAAS,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM2B,mBAAmB,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd2B,gBAAgB,CAAC,CAAC;IAClBC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAME,KAAK,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACJ,gBAAgB,CAAC,CAAC;IACzDX,cAAc,CAACa,KAAK,CAAC;EACvB,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,OAAO,GAAG,MAAMH,MAAM,CAACC,WAAW,CAACC,kBAAkB,CAAC,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACX,MAAMN,gBAAgB,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMnB,OAAO,GAAG,MAAMqB,MAAM,CAACC,WAAW,CAACG,UAAU,CAAC,CAAC;IACrDxB,UAAU,CAACD,OAAO,CAAC;IACnB,IAAIA,OAAO,CAAC0B,MAAM,GAAG,CAAC,IAAI,CAACxB,cAAc,EAAE;MACzC;MACA,MAAMyB,MAAM,GAAG3B,OAAO,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;MACxF5B,iBAAiB,CAACwB,MAAM,IAAI3B,OAAO,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMgC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAC9B,cAAc,EAAE;MACnBO,SAAS,CAAC,4CAA4C,CAAC;MACvD;IACF;IAEA,IAAI;MACFA,SAAS,CAAC,uBAAuB,CAAC;MAClCE,oBAAoB,CAAC,CAAC,CAAC;;MAEvB;MACAM,mBAAmB,CAACgB,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC9CvB,oBAAoB,CAACwB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;UACLC,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAE1C,cAAc,CAAC2C,EAAE;YACtCC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,IAAI;YACfC,YAAY,EAAE;UAChB;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIC,WAAW,GAAG,IAAI;MACtB,IAAI;QACFA,WAAW,GAAG,MAAMd,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACtDC,KAAK,EAAE;YACLY,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE;UACnB,CAAC;UACDb,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOc,UAAU,EAAE;QACnBC,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;MAChE;;MAEA;MACA,IAAIC,cAAc,GAAGtB,MAAM;MAC3B,IAAIe,WAAW,EAAE;QACf,MAAMQ,WAAW,GAAGR,WAAW,CAACS,cAAc,CAAC,CAAC;QAChDD,WAAW,CAACE,OAAO,CAACC,KAAK,IAAIJ,cAAc,CAACK,QAAQ,CAACD,KAAK,CAAC,CAAC;MAC9D;MAEA9C,SAAS,CAACiB,OAAO,GAAGyB,cAAc;;MAElC;MACA,MAAMM,aAAa,GAAG,IAAIC,aAAa,CAACP,cAAc,EAAE;QACtDQ,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG,EAAE;MAEjBH,aAAa,CAACI,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBJ,MAAM,CAACK,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzB;MACF,CAAC;MAEDN,aAAa,CAACS,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACR,MAAM,EAAE;UAAES,IAAI,EAAE;QAAa,CAAC,CAAC;QACrDvE,eAAe,CAACqE,IAAI,CAAC;QACrBjE,SAAS,CAAC,2CAA2C,CAAC;;QAEtD;QACAoE,UAAU,CAAC,MAAM;UACf,IAAI/D,QAAQ,CAACmB,OAAO,IAAIyC,IAAI,EAAE;YAC5B,MAAMI,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;YAC1C5D,QAAQ,CAACmB,OAAO,CAACgD,GAAG,GAAGH,QAAQ;YAC/BhE,QAAQ,CAACmB,OAAO,CAACiD,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3B;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MAEDnE,gBAAgB,CAACkB,OAAO,GAAG+B,aAAa;MACxCA,aAAa,CAACmB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE1BpF,cAAc,CAAC,IAAI,CAAC;MACpBU,SAAS,CAAC,uBAAuB,CAAC;IAEpC,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD3E,SAAS,CAAC,sDAAsD,CAAC;MACjE,IAAIQ,mBAAmB,CAACgB,OAAO,EAAE;QAC/BoD,aAAa,CAACpE,mBAAmB,CAACgB,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMqD,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIvE,gBAAgB,CAACkB,OAAO,IAAInC,WAAW,EAAE;MAC3CiB,gBAAgB,CAACkB,OAAO,CAACsD,IAAI,CAAC,CAAC;;MAE/B;MACA,IAAItE,mBAAmB,CAACgB,OAAO,EAAE;QAC/BoD,aAAa,CAACpE,mBAAmB,CAACgB,OAAO,CAAC;MAC5C;;MAEA;MACA,IAAIjB,SAAS,CAACiB,OAAO,EAAE;QACrBjB,SAAS,CAACiB,OAAO,CAACuD,SAAS,CAAC,CAAC,CAAC3B,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACyB,IAAI,CAAC,CAAC,CAAC;MAC9D;MAEAxF,cAAc,CAAC,KAAK,CAAC;MACrBU,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC;EAED,MAAMgF,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,GAAG,MAAM,KAAK;IACnD,IAAI9F,YAAY,EAAE;MAChB,IAAI;QACF,IAAI8F,MAAM,KAAK,MAAM,EAAE;UACrB;UACA,MAAMC,GAAG,GAAGpB,GAAG,CAACC,eAAe,CAAC5E,YAAY,CAAC;UAC7C,MAAMgG,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGJ,GAAG;UACZC,CAAC,CAACI,QAAQ,GAAG,aAAa,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UACzFR,CAAC,CAACS,KAAK,CAAC,CAAC;UACT9B,GAAG,CAAC+B,eAAe,CAACX,GAAG,CAAC;UACxBtF,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,MAAM,IAAIqF,MAAM,KAAK,KAAK,EAAE;UAC3BzF,SAAS,CAAC,sBAAsB,CAAC;;UAEjC;UACA,MAAMsG,WAAW,GAAG,MAAM3G,YAAY,CAAC2G,WAAW,CAAC,CAAC;UACpD,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACF,WAAW,CAAC;;UAE9C;UACA,MAAMG,QAAQ,GAAG,aAAa,IAAIT,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UAC7F,MAAMO,SAAS,GAAG,MAAM9F,MAAM,CAACC,WAAW,CAAC8F,aAAa,CAACJ,UAAU,EAAEE,QAAQ,CAAC;UAE9E,IAAIC,SAAS,EAAE;YACb1G,SAAS,CAAC,yBAAyB,CAAC;YACpCI,oBAAoB,CAAC,KAAK,CAAC;UAC7B;QACF;MACF,CAAC,CAAC,OAAOuE,KAAK,EAAE;QACd5B,OAAO,CAAC4B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD3E,SAAS,CAAC,0BAA0B,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAM4G,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAIvG,QAAQ,CAACmB,OAAO,IAAInB,QAAQ,CAACmB,OAAO,CAACgD,GAAG,EAAE;MAC5CF,GAAG,CAAC+B,eAAe,CAAChG,QAAQ,CAACmB,OAAO,CAACgD,GAAG,CAAC;MACzCnE,QAAQ,CAACmB,OAAO,CAACgD,GAAG,GAAG,EAAE;IAC3B;IACA5E,eAAe,CAAC,IAAI,CAAC;IACrBI,SAAS,CAAC,iBAAiB,CAAC;EAC9B,CAAC;EAED,oBACEhB,OAAA;IAAK6H,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB9H,OAAA;MAAQ6H,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACxB9H,OAAA;QAAA8H,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBlI,OAAA;QAAK6H,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBjH,WAAW,CAACqB,MAAM,KAAK,QAAQ,iBAC9BlC,OAAA;UAAQ6H,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAErG,kBAAmB;UAAAgG,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETlI,OAAA;MAAM6H,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACvB9H,OAAA;QAAK6H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B9H,OAAA;UAAA8H,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjClI,OAAA;UAAK6H,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BvH,OAAO,CAAC6H,GAAG,CAAEC,MAAM,iBAClBrI,OAAA;YAEE6H,SAAS,EAAE,eAAe,CAAApH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C,EAAE,MAAKiF,MAAM,CAACjF,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/E+E,OAAO,EAAEA,CAAA,KAAMzH,iBAAiB,CAAC2H,MAAM,CAAE;YAAAP,QAAA,gBAEzC9H,OAAA;cACEwF,GAAG,EAAE6C,MAAM,CAACC,SAAU;cACtBC,GAAG,EAAEF,MAAM,CAAChG,IAAK;cACjBwF,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACFlI,OAAA;cAAK6H,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEO,MAAM,CAAChG;YAAI;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAT3CG,MAAM,CAACjF,EAAE;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlI,OAAA;QAAK6H,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BnH,YAAY,gBACXX,OAAA;UAAA8H,QAAA,gBACE9H,OAAA;YACEwI,GAAG,EAAEnH,QAAS;YACdwG,SAAS,EAAC,eAAe;YACzBY,QAAQ;YACRC,QAAQ,EAAE,KAAM;YAChBC,OAAO,EAAC,UAAU;YAClBC,KAAK;YACLC,WAAW;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACFlI,OAAA;YAAK6H,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B9H,OAAA;cAAA8H,QAAA,GAAG,uCAAgC,EAAC9B,cAAc,CAAC/E,iBAAiB,CAAC;YAAA;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENlI,OAAA;UAAK6H,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B9H,OAAA;YAAK6H,SAAS,EAAE,cAAcxH,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;YAAAyH,QAAA,EAC5DzH,WAAW,gBACVL,OAAA;cAAK6H,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B9H,OAAA;gBAAK6H,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC9H,OAAA;kBAAM6H,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,aAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlI,OAAA;gBAAK6H,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAChC9B,cAAc,CAAC/E,iBAAiB;cAAC;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNlI,OAAA;gBAAK6H,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BrH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4B;cAAI;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENlI,OAAA;cAAK6H,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B9H,OAAA;gBAAK6H,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE/G;cAAM;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1CzH,cAAc,iBACbT,OAAA;gBAAK6H,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,mBAAiB,EAACrH,cAAc,CAAC4B,IAAI;cAAA;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlI,OAAA;QAAK6H,SAAS,EAAC,UAAU;QAAAC,QAAA,GACtB,CAACzH,WAAW,IAAI,CAACM,YAAY,iBAC5BX,OAAA;UACE6H,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAE5F,cAAe;UACxBuG,QAAQ,EAAE,CAACrI,cAAc,IAAII,WAAW,CAACqB,MAAM,KAAK,QAAS;UAAA4F,QAAA,EAC9D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEA7H,WAAW,iBACVL,OAAA;UAAQ6H,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAEtC,aAAc;UAAAiC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAvH,YAAY,IAAI,CAACN,WAAW,iBAC3BL,OAAA,CAAAE,SAAA;UAAA4H,QAAA,GACG,CAAC3G,iBAAiB,gBACjBnB,OAAA;YACE6H,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAM/G,oBAAoB,CAAC,IAAI,CAAE;YAAA0G,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAETlI,OAAA;YAAK6H,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9H,OAAA;cAAA8H,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBlI,OAAA;cACE6H,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC,MAAM,CAAE;cAAAsB,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlI,OAAA;cACE6H,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC,KAAK,CAAE;cAAAsB,QAAA,EACzC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlI,OAAA;cACE6H,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAEA,CAAA,KAAM/G,oBAAoB,CAAC,KAAK,CAAE;cAAA0G,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDlI,OAAA;YAAQ6H,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAEP,cAAe;YAAAE,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,eAEDlI,OAAA;UAAQ6H,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAEzG,WAAY;UAAAoG,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETlI,OAAA;UACE6H,SAAS,EAAC,KAAK;UACfkB,KAAK,EAAE;YAACC,UAAU,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAO,CAAE;UAC9Cd,OAAO,EAAEA,CAAA,KAAMe,KAAK,CAAC,iCAAiC,CAAE;UAAApB,QAAA,EACzD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC9H,EAAA,CAvWQD,GAAG;AAAAgJ,EAAA,GAAHhJ,GAAG;AAyWZ,eAAeA,GAAG;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}