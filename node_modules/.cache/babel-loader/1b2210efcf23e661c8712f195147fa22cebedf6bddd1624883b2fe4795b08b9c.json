{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js\",\n  _s = $RefreshSig$();\n/// <reference path=\"./electron.d.ts\" />\nimport { useState, useRef, useEffect } from 'react';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n\n  // New Phase 2 state variables\n  const [recordingSettings, setRecordingSettings] = useState({\n    includeSystemAudio: false,\n    includeMicrophone: true,\n    includeWebcam: false,\n    resolution: '1920x1080',\n    frameRate: 30,\n    quality: 'high',\n    selectedMicrophone: 'default',\n    selectedCamera: 'default',\n    selectedSpeaker: 'default'\n  });\n  const [webcamStream, setWebcamStream] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [audioLevels, setAudioLevels] = useState({\n    microphone: 0,\n    system: 0\n  });\n  const [tempSettings, setTempSettings] = useState(null);\n  const [availableDevices, setAvailableDevices] = useState({\n    microphones: [],\n    cameras: [],\n    speakers: []\n  });\n  const videoRef = useRef(null);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n    loadAvailableDevices();\n  }, []);\n  const loadAvailableDevices = async () => {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const microphones = devices.filter(device => device.kind === 'audioinput').map(device => ({\n        id: device.deviceId,\n        label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,\n        groupId: device.groupId\n      }));\n      const cameras = devices.filter(device => device.kind === 'videoinput').map(device => ({\n        id: device.deviceId,\n        label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,\n        groupId: device.groupId\n      }));\n      const speakers = devices.filter(device => device.kind === 'audiooutput').map(device => ({\n        id: device.deviceId,\n        label: device.label || `Speaker ${device.deviceId.slice(0, 8)}`,\n        groupId: device.groupId\n      }));\n      setAvailableDevices({\n        microphones: [{\n          id: 'default',\n          label: 'Default Microphone',\n          groupId: null\n        }, ...microphones],\n        cameras: [{\n          id: 'default',\n          label: 'Default Camera',\n          groupId: null\n        }, ...cameras],\n        speakers: [{\n          id: 'default',\n          label: 'Default Speaker',\n          groupId: null\n        }, ...speakers]\n      });\n    } catch (error) {\n      console.warn('Failed to enumerate devices:', error);\n      // Set default options if enumeration fails\n      setAvailableDevices({\n        microphones: [{\n          id: 'default',\n          label: 'Default Microphone',\n          groupId: null\n        }],\n        cameras: [{\n          id: 'default',\n          label: 'Default Camera',\n          groupId: null\n        }],\n        speakers: [{\n          id: 'default',\n          label: 'Default Speaker',\n          groupId: null\n        }]\n      });\n    }\n  };\n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n\n    // Show status message about permissions\n    const permissionStatus = [];\n    if (perms.screen === 'granted') permissionStatus.push('✅ Screen recording');else permissionStatus.push('❌ Screen recording');\n    if (perms.microphone === 'granted') permissionStatus.push('✅ Microphone');else permissionStatus.push('❌ Microphone');\n    if (perms.camera === 'granted') permissionStatus.push('✅ Camera');else permissionStatus.push('❌ Camera');\n    setStatus(`Permissions: ${permissionStatus.join(', ')}`);\n\n    // Reset status after 3 seconds\n    setTimeout(() => {\n      if (!isRecording && !recordedBlob) {\n        setStatus('Ready to record');\n      }\n    }, 3000);\n  };\n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    try {\n      // Start countdown if enabled\n      if (countdown > 0) {\n        await startCountdown();\n      }\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Parse resolution settings\n      const [width, height] = recordingSettings.resolution.split('x').map(Number);\n\n      // Get screen stream (without system audio for now - will implement properly in Phase 3)\n      const screenConstraints = {\n        audio: false,\n        // System audio capture needs more complex implementation\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: width,\n            maxWidth: width,\n            minHeight: height,\n            maxHeight: height,\n            minFrameRate: recordingSettings.frameRate,\n            maxFrameRate: recordingSettings.frameRate\n          }\n        }\n      };\n      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);\n\n      // Get microphone stream if enabled\n      let microphoneStream = null;\n      if (recordingSettings.includeMicrophone) {\n        try {\n          microphoneStream = await navigator.mediaDevices.getUserMedia({\n            audio: {\n              echoCancellation: true,\n              noiseSuppression: true,\n              autoGainControl: true,\n              sampleRate: 48000\n            },\n            video: false\n          });\n        } catch (audioError) {\n          console.warn('Microphone access denied, recording without microphone');\n        }\n      }\n\n      // Get webcam stream if enabled\n      let webcamStreamLocal = null;\n      if (recordingSettings.includeWebcam) {\n        try {\n          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({\n            video: {\n              width: {\n                ideal: 320\n              },\n              height: {\n                ideal: 240\n              },\n              frameRate: {\n                ideal: 30\n              }\n            },\n            audio: false\n          });\n          setWebcamStream(webcamStreamLocal);\n\n          // Display webcam preview\n          if (webcamRef.current) {\n            webcamRef.current.srcObject = webcamStreamLocal;\n          }\n        } catch (webcamError) {\n          console.warn('Webcam access denied, recording without webcam');\n        }\n      }\n\n      // Combine all streams\n      let combinedStream = screenStream;\n\n      // Add microphone audio if available\n      if (microphoneStream) {\n        const audioTracks = microphoneStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n\n        // Setup audio level monitoring\n        setupAudioMonitoring(microphoneStream);\n      }\n\n      // Note: Webcam video will be composited in post-processing for now\n      // In a future version, we could use Canvas API to composite in real-time\n\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder with quality settings\n      const mimeType = getOptimalMimeType();\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType,\n        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)\n      });\n      const chunks = [];\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, {\n          type: mimeType\n        });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Clean up webcam stream\n        if (webcamStreamLocal) {\n          webcamStreamLocal.getTracks().forEach(track => track.stop());\n          setWebcamStream(null);\n        }\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n\n      // Stop webcam stream\n      if (webcamStream) {\n        webcamStream.getTracks().forEach(track => track.stop());\n        setWebcamStream(null);\n      }\n\n      // Clean up audio context\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n        analyserRef.current = null;\n      }\n\n      // Reset audio levels\n      setAudioLevels({\n        microphone: 0,\n        system: 0\n      });\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Helper functions for Phase 2 features\n  const startCountdown = () => {\n    return new Promise(resolve => {\n      let count = countdown;\n      setStatus(`Recording starts in ${count}...`);\n      const countdownInterval = setInterval(() => {\n        count--;\n        if (count > 0) {\n          setStatus(`Recording starts in ${count}...`);\n        } else {\n          setStatus('Recording starting...');\n          clearInterval(countdownInterval);\n          resolve();\n        }\n      }, 1000);\n    });\n  };\n  const getOptimalMimeType = () => {\n    const types = ['video/webm;codecs=vp9,opus', 'video/webm;codecs=vp8,opus', 'video/webm;codecs=h264,opus', 'video/webm'];\n    for (const type of types) {\n      if (MediaRecorder.isTypeSupported(type)) {\n        return type;\n      }\n    }\n    return 'video/webm';\n  };\n  const getVideoBitrate = (quality, width, height) => {\n    const pixelCount = width * height;\n    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels\n\n    switch (quality) {\n      case 'draft':\n        return Math.floor(baseRate * 0.5) * 1000;\n      // 0.5 bits per pixel\n      case 'standard':\n        return Math.floor(baseRate * 1) * 1000;\n      // 1 bit per pixel\n      case 'high':\n        return Math.floor(baseRate * 2) * 1000;\n      // 2 bits per pixel\n      default:\n        return Math.floor(baseRate * 1) * 1000;\n    }\n  };\n  const setupAudioMonitoring = audioStream => {\n    if (!audioStream) return;\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const analyser = audioContext.createAnalyser();\n      const source = audioContext.createMediaStreamSource(audioStream);\n      analyser.fftSize = 256;\n      source.connect(analyser);\n      audioContextRef.current = audioContext;\n      analyserRef.current = analyser;\n\n      // Start monitoring audio levels\n      const monitorAudio = () => {\n        if (!analyserRef.current) return;\n        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\n        analyserRef.current.getByteFrequencyData(dataArray);\n\n        // Calculate average volume\n        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;\n        const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1\n\n        setAudioLevels(prev => ({\n          ...prev,\n          microphone: normalizedLevel\n        }));\n        if (isRecording) {\n          requestAnimationFrame(monitorAudio);\n        }\n      };\n      monitorAudio();\n    } catch (error) {\n      console.warn('Audio monitoring setup failed:', error);\n    }\n  };\n\n  // Settings modal functions\n  const openSettings = () => {\n    setTempSettings({\n      ...recordingSettings,\n      countdown\n    });\n    setShowSettings(true);\n  };\n  const closeSettings = () => {\n    setShowSettings(false);\n    setTempSettings(null);\n  };\n  const saveSettings = () => {\n    if (tempSettings) {\n      setRecordingSettings({\n        includeSystemAudio: tempSettings.includeSystemAudio,\n        includeMicrophone: tempSettings.includeMicrophone,\n        includeWebcam: tempSettings.includeWebcam,\n        resolution: tempSettings.resolution,\n        frameRate: tempSettings.frameRate,\n        quality: tempSettings.quality\n      });\n      setCountdown(tempSettings.countdown);\n    }\n    setShowSettings(false);\n    setTempSettings(null);\n  };\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Screen Recorder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: checkPermissions,\n          children: \"\\uD83D\\uDD12 Check Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: openSettings,\n          disabled: isRecording,\n          children: \"\\u2699\\uFE0F Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), permissions.screen === 'denied' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: requestPermissions,\n          children: \"Grant Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: closeSettings,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Recording Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close\",\n            onClick: closeSettings,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Audio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.includeMicrophone) || false,\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  includeMicrophone: e.target.checked\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), \"Include Microphone\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"setting-item disabled\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: false,\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), \"Include System Audio\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-note\",\n                children: \"(Coming in Phase 3)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.includeWebcam) || false,\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  includeWebcam: e.target.checked\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 19\n              }, this), \"Include Webcam\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Resolution:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.resolution) || '1920x1080',\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  resolution: e.target.value\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1280x720\",\n                  children: \"720p (1280x720)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1920x1080\",\n                  children: \"1080p (1920x1080)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"2560x1440\",\n                  children: \"1440p (2560x1440)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"3840x2160\",\n                  children: \"4K (3840x2160)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Frame Rate:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.frameRate) || 30,\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  frameRate: parseInt(e.target.value)\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 30,\n                  children: \"30 FPS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 60,\n                  children: \"60 FPS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Quality:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.quality) || 'high',\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  quality: e.target.value\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"draft\",\n                  children: \"Draft (Smaller file)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"standard\",\n                  children: \"Standard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high\",\n                  children: \"High Quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Recording\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Countdown Timer (seconds):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: (tempSettings === null || tempSettings === void 0 ? void 0 : tempSettings.countdown) || 0,\n                onChange: e => setTempSettings(prev => ({\n                  ...prev,\n                  countdown: parseInt(e.target.value)\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 0,\n                  children: \"No countdown\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 3,\n                  children: \"3 seconds\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 5,\n                  children: \"5 seconds\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 10,\n                  children: \"10 seconds\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: closeSettings,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: saveSettings,\n            children: \"Save Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"source-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select Screen or Window:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sources-grid\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `source-item ${(selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.id) === source.id ? 'selected' : ''}`,\n            onClick: () => setSelectedSource(source),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: source.thumbnail,\n              alt: source.name,\n              className: \"source-thumbnail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"source-name\",\n              children: source.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 17\n            }, this)]\n          }, source.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-area\",\n        children: recordedBlob ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: videoRef,\n            className: \"preview-video\",\n            controls: true,\n            autoPlay: false,\n            preload: \"metadata\",\n            muted: true,\n            playsInline: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recording-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Recording completed \\u2022 Duration: \", formatDuration(recordingDuration)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status-box ${isRecording ? 'recording' : ''}`,\n            children: isRecording ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"recording-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"recording-dot\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 23\n                }, this), \"RECORDING\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-duration\",\n                children: formatDuration(recordingDuration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-source\",\n                children: selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-settings-info\",\n                children: [recordingSettings.resolution, \" \\u2022 \", recordingSettings.frameRate, \"fps \\u2022 \", recordingSettings.quality]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 21\n              }, this), (recordingSettings.includeMicrophone || recordingSettings.includeSystemAudio) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audio-levels\",\n                children: [recordingSettings.includeMicrophone && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audio-level-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audio-label\",\n                    children: \"\\uD83C\\uDFA4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audio-meter\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"audio-level-bar\",\n                      style: {\n                        width: `${audioLevels.microphone * 100}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 717,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 27\n                }, this), recordingSettings.includeSystemAudio && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audio-level-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"audio-label\",\n                    children: \"\\uD83D\\uDD0A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audio-meter\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"audio-level-bar\",\n                      style: {\n                        width: `${audioLevels.system * 100}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 728,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 727,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ready-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-text\",\n                children: status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 21\n              }, this), selectedSource && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-source\",\n                children: [\"Ready to record: \", selectedSource.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 15\n          }, this), webcamStream && isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webcam-preview\",\n            children: [/*#__PURE__*/_jsxDEV(\"video\", {\n              ref: webcamRef,\n              className: \"webcam-video\",\n              autoPlay: true,\n              muted: true,\n              playsInline: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"webcam-label\",\n              children: \"Webcam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        children: [!isRecording && !recordedBlob && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: startRecording,\n          disabled: !selectedSource || permissions.screen === 'denied',\n          children: \"Start Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 13\n        }, this), isRecording && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger\",\n          onClick: stopRecording,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 13\n        }, this), recordedBlob && !isRecording && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [!showExportOptions ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowExportOptions(true),\n            children: \"Download Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"export-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Choose format:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('webm'),\n              children: \"Download WebM (Fast)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('mp4'),\n              children: \"Download MP4 (Compatible)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => setShowExportOptions(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearRecording,\n            children: \"New Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: loadSources,\n          children: \"Refresh Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 504,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"v3s4+iu/bt1uAESz7UtyxZkNYsc=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isRecording", "setIsRecording", "sources", "setSources", "selectedSource", "setSelectedSource", "recordedBlob", "setRecordedBlob", "permissions", "setPermissions", "status", "setStatus", "recordingDuration", "setRecordingDuration", "showExportOptions", "setShowExportOptions", "recordingSettings", "setRecordingSettings", "includeSystemAudio", "includeMicrophone", "includeWebcam", "resolution", "frameRate", "quality", "selectedMicrophone", "selectedCamera", "selectedSpeaker", "webcamStream", "setWebcamStream", "countdown", "setCountdown", "showSettings", "setShowSettings", "audioLevels", "setAudioLevels", "microphone", "system", "tempSettings", "setTempSettings", "availableDevices", "setAvailableDevices", "microphones", "cameras", "speakers", "videoRef", "webcamRef", "mediaRecorderRef", "streamRef", "durationIntervalRef", "audioContextRef", "analyserRef", "checkPermissions", "loadSources", "loadAvailableDevices", "devices", "navigator", "mediaDevices", "enumerateDevices", "filter", "device", "kind", "map", "id", "deviceId", "label", "slice", "groupId", "error", "console", "warn", "perms", "window", "electronAPI", "permissionStatus", "screen", "push", "camera", "join", "setTimeout", "requestPermissions", "granted", "getSources", "length", "find", "s", "name", "includes", "startRecording", "startCountdown", "current", "setInterval", "prev", "width", "height", "split", "Number", "screenConstraints", "audio", "video", "mandatory", "chromeMediaSource", "chromeMediaSourceId", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "minFrameRate", "maxFrameRate", "screenStream", "getUserMedia", "microphoneStream", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "audioError", "webcamStreamLocal", "ideal", "srcObject", "webcamError", "combinedStream", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "addTrack", "setupAudioMonitoring", "mimeType", "getOptimalMimeType", "mediaRecorder", "MediaRecorder", "videoBitsPerSecond", "getVideoBitrate", "chunks", "ondataavailable", "event", "data", "size", "onstop", "blob", "Blob", "type", "getTracks", "stop", "videoUrl", "URL", "createObjectURL", "src", "load", "start", "clearInterval", "stopRecording", "close", "formatDuration", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "Promise", "resolve", "count", "countdownInterval", "types", "isTypeSupported", "pixelCount", "baseRate", "audioStream", "audioContext", "AudioContext", "webkitAudioContext", "analyser", "create<PERSON><PERSON>yser", "source", "createMediaStreamSource", "fftSize", "connect", "monitorAudio", "dataArray", "Uint8Array", "frequencyBinCount", "getByteFrequencyData", "average", "reduce", "sum", "value", "normalizedLevel", "min", "requestAnimationFrame", "openSettings", "closeSettings", "saveSettings", "downloadRecording", "format", "url", "a", "document", "createElement", "href", "download", "Date", "toISOString", "replace", "click", "revokeObjectURL", "arrayBuffer", "uint8Array", "filename", "savedPath", "saveRecording", "clearRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "e", "stopPropagation", "checked", "onChange", "target", "parseInt", "thumbnail", "alt", "ref", "controls", "autoPlay", "preload", "muted", "playsInline", "style", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js"], "sourcesContent": ["/// <reference path=\"./electron.d.ts\" />\nimport { useState, useRef, useEffect } from 'react';\nimport './index.css';\n\nfunction App() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n\n  // New Phase 2 state variables\n  const [recordingSettings, setRecordingSettings] = useState({\n    includeSystemAudio: false,\n    includeMicrophone: true,\n    includeWebcam: false,\n    resolution: '1920x1080',\n    frameRate: 30,\n    quality: 'high',\n    selectedMicrophone: 'default',\n    selectedCamera: 'default',\n    selectedSpeaker: 'default'\n  });\n  const [webcamStream, setWebcamStream] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [audioLevels, setAudioLevels] = useState({ microphone: 0, system: 0 });\n  const [tempSettings, setTempSettings] = useState(null);\n  const [availableDevices, setAvailableDevices] = useState({\n    microphones: [],\n    cameras: [],\n    speakers: []\n  });\n\n  const videoRef = useRef(null);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  \n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n    loadAvailableDevices();\n  }, []);\n\n  const loadAvailableDevices = async () => {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n\n      const microphones = devices\n        .filter(device => device.kind === 'audioinput')\n        .map(device => ({\n          id: device.deviceId,\n          label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,\n          groupId: device.groupId\n        }));\n\n      const cameras = devices\n        .filter(device => device.kind === 'videoinput')\n        .map(device => ({\n          id: device.deviceId,\n          label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,\n          groupId: device.groupId\n        }));\n\n      const speakers = devices\n        .filter(device => device.kind === 'audiooutput')\n        .map(device => ({\n          id: device.deviceId,\n          label: device.label || `Speaker ${device.deviceId.slice(0, 8)}`,\n          groupId: device.groupId\n        }));\n\n      setAvailableDevices({\n        microphones: [\n          { id: 'default', label: 'Default Microphone', groupId: null },\n          ...microphones\n        ],\n        cameras: [\n          { id: 'default', label: 'Default Camera', groupId: null },\n          ...cameras\n        ],\n        speakers: [\n          { id: 'default', label: 'Default Speaker', groupId: null },\n          ...speakers\n        ]\n      });\n    } catch (error) {\n      console.warn('Failed to enumerate devices:', error);\n      // Set default options if enumeration fails\n      setAvailableDevices({\n        microphones: [{ id: 'default', label: 'Default Microphone', groupId: null }],\n        cameras: [{ id: 'default', label: 'Default Camera', groupId: null }],\n        speakers: [{ id: 'default', label: 'Default Speaker', groupId: null }]\n      });\n    }\n  };\n  \n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n\n    // Show status message about permissions\n    const permissionStatus = [];\n    if (perms.screen === 'granted') permissionStatus.push('✅ Screen recording');\n    else permissionStatus.push('❌ Screen recording');\n\n    if (perms.microphone === 'granted') permissionStatus.push('✅ Microphone');\n    else permissionStatus.push('❌ Microphone');\n\n    if (perms.camera === 'granted') permissionStatus.push('✅ Camera');\n    else permissionStatus.push('❌ Camera');\n\n    setStatus(`Permissions: ${permissionStatus.join(', ')}`);\n\n    // Reset status after 3 seconds\n    setTimeout(() => {\n      if (!isRecording && !recordedBlob) {\n        setStatus('Ready to record');\n      }\n    }, 3000);\n  };\n  \n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  \n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  \n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n\n    try {\n      // Start countdown if enabled\n      if (countdown > 0) {\n        await startCountdown();\n      }\n\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Parse resolution settings\n      const [width, height] = recordingSettings.resolution.split('x').map(Number);\n\n      // Get screen stream (without system audio for now - will implement properly in Phase 3)\n      const screenConstraints = {\n        audio: false, // System audio capture needs more complex implementation\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: width,\n            maxWidth: width,\n            minHeight: height,\n            maxHeight: height,\n            minFrameRate: recordingSettings.frameRate,\n            maxFrameRate: recordingSettings.frameRate\n          }\n        }\n      };\n\n      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);\n\n      // Get microphone stream if enabled\n      let microphoneStream = null;\n      if (recordingSettings.includeMicrophone) {\n        try {\n          microphoneStream = await navigator.mediaDevices.getUserMedia({\n            audio: {\n              echoCancellation: true,\n              noiseSuppression: true,\n              autoGainControl: true,\n              sampleRate: 48000\n            },\n            video: false\n          });\n        } catch (audioError) {\n          console.warn('Microphone access denied, recording without microphone');\n        }\n      }\n\n      // Get webcam stream if enabled\n      let webcamStreamLocal = null;\n      if (recordingSettings.includeWebcam) {\n        try {\n          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({\n            video: {\n              width: { ideal: 320 },\n              height: { ideal: 240 },\n              frameRate: { ideal: 30 }\n            },\n            audio: false\n          });\n          setWebcamStream(webcamStreamLocal);\n\n          // Display webcam preview\n          if (webcamRef.current) {\n            webcamRef.current.srcObject = webcamStreamLocal;\n          }\n        } catch (webcamError) {\n          console.warn('Webcam access denied, recording without webcam');\n        }\n      }\n\n      // Combine all streams\n      let combinedStream = screenStream;\n\n      // Add microphone audio if available\n      if (microphoneStream) {\n        const audioTracks = microphoneStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n\n        // Setup audio level monitoring\n        setupAudioMonitoring(microphoneStream);\n      }\n\n      // Note: Webcam video will be composited in post-processing for now\n      // In a future version, we could use Canvas API to composite in real-time\n\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder with quality settings\n      const mimeType = getOptimalMimeType();\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType,\n        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)\n      });\n\n      const chunks = [];\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, { type: mimeType });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Clean up webcam stream\n        if (webcamStreamLocal) {\n          webcamStreamLocal.getTracks().forEach(track => track.stop());\n          setWebcamStream(null);\n        }\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  \n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n\n      // Stop webcam stream\n      if (webcamStream) {\n        webcamStream.getTracks().forEach(track => track.stop());\n        setWebcamStream(null);\n      }\n\n      // Clean up audio context\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n        analyserRef.current = null;\n      }\n\n      // Reset audio levels\n      setAudioLevels({ microphone: 0, system: 0 });\n\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  \n  const formatDuration = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Helper functions for Phase 2 features\n  const startCountdown = () => {\n    return new Promise((resolve) => {\n      let count = countdown;\n      setStatus(`Recording starts in ${count}...`);\n\n      const countdownInterval = setInterval(() => {\n        count--;\n        if (count > 0) {\n          setStatus(`Recording starts in ${count}...`);\n        } else {\n          setStatus('Recording starting...');\n          clearInterval(countdownInterval);\n          resolve();\n        }\n      }, 1000);\n    });\n  };\n\n  const getOptimalMimeType = () => {\n    const types = [\n      'video/webm;codecs=vp9,opus',\n      'video/webm;codecs=vp8,opus',\n      'video/webm;codecs=h264,opus',\n      'video/webm'\n    ];\n\n    for (const type of types) {\n      if (MediaRecorder.isTypeSupported(type)) {\n        return type;\n      }\n    }\n    return 'video/webm';\n  };\n\n  const getVideoBitrate = (quality, width, height) => {\n    const pixelCount = width * height;\n    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels\n\n    switch (quality) {\n      case 'draft':\n        return Math.floor(baseRate * 0.5) * 1000; // 0.5 bits per pixel\n      case 'standard':\n        return Math.floor(baseRate * 1) * 1000; // 1 bit per pixel\n      case 'high':\n        return Math.floor(baseRate * 2) * 1000; // 2 bits per pixel\n      default:\n        return Math.floor(baseRate * 1) * 1000;\n    }\n  };\n\n  const setupAudioMonitoring = (audioStream) => {\n    if (!audioStream) return;\n\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const analyser = audioContext.createAnalyser();\n      const source = audioContext.createMediaStreamSource(audioStream);\n\n      analyser.fftSize = 256;\n      source.connect(analyser);\n\n      audioContextRef.current = audioContext;\n      analyserRef.current = analyser;\n\n      // Start monitoring audio levels\n      const monitorAudio = () => {\n        if (!analyserRef.current) return;\n\n        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\n        analyserRef.current.getByteFrequencyData(dataArray);\n\n        // Calculate average volume\n        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;\n        const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1\n\n        setAudioLevels(prev => ({\n          ...prev,\n          microphone: normalizedLevel\n        }));\n\n        if (isRecording) {\n          requestAnimationFrame(monitorAudio);\n        }\n      };\n\n      monitorAudio();\n    } catch (error) {\n      console.warn('Audio monitoring setup failed:', error);\n    }\n  };\n\n  // Settings modal functions\n  const openSettings = () => {\n    setTempSettings({ ...recordingSettings, countdown });\n    setShowSettings(true);\n  };\n\n  const closeSettings = () => {\n    setShowSettings(false);\n    setTempSettings(null);\n  };\n\n  const saveSettings = () => {\n    if (tempSettings) {\n      setRecordingSettings({\n        includeSystemAudio: tempSettings.includeSystemAudio,\n        includeMicrophone: tempSettings.includeMicrophone,\n        includeWebcam: tempSettings.includeWebcam,\n        resolution: tempSettings.resolution,\n        frameRate: tempSettings.frameRate,\n        quality: tempSettings.quality\n      });\n      setCountdown(tempSettings.countdown);\n    }\n    setShowSettings(false);\n    setTempSettings(null);\n  };\n\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  \n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  \n  return (\n    <div className=\"app\">\n      <header className=\"header\">\n        <h1>Screen Recorder</h1>\n        <div className=\"header-controls\">\n          <button\n            className=\"btn btn-secondary\"\n            onClick={checkPermissions}\n          >\n            🔒 Check Permissions\n          </button>\n          <button\n            className=\"btn btn-secondary\"\n            onClick={openSettings}\n            disabled={isRecording}\n          >\n            ⚙️ Settings\n          </button>\n          {permissions.screen === 'denied' && (\n            <button className=\"btn btn-primary\" onClick={requestPermissions}>\n              Grant Permissions\n            </button>\n          )}\n        </div>\n      </header>\n\n      {showSettings && (\n        <div className=\"modal-overlay\" onClick={closeSettings}>\n          <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n            <div className=\"modal-header\">\n              <h3>Recording Settings</h3>\n              <button className=\"modal-close\" onClick={closeSettings}>×</button>\n            </div>\n\n            <div className=\"modal-body\">\n              <div className=\"settings-group\">\n                <h4>Audio</h4>\n                <label className=\"setting-item\">\n                  <input\n                    type=\"checkbox\"\n                    checked={tempSettings?.includeMicrophone || false}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      includeMicrophone: e.target.checked\n                    }))}\n                  />\n                  Include Microphone\n                </label>\n                <label className=\"setting-item disabled\">\n                  <input\n                    type=\"checkbox\"\n                    checked={false}\n                    disabled={true}\n                  />\n                  Include System Audio\n                  <span className=\"feature-note\">(Coming in Phase 3)</span>\n                </label>\n              </div>\n\n              <div className=\"settings-group\">\n                <h4>Video</h4>\n                <label className=\"setting-item\">\n                  <input\n                    type=\"checkbox\"\n                    checked={tempSettings?.includeWebcam || false}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      includeWebcam: e.target.checked\n                    }))}\n                  />\n                  Include Webcam\n                </label>\n\n                <div className=\"setting-item\">\n                  <label>Resolution:</label>\n                  <select\n                    value={tempSettings?.resolution || '1920x1080'}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      resolution: e.target.value\n                    }))}\n                  >\n                    <option value=\"1280x720\">720p (1280x720)</option>\n                    <option value=\"1920x1080\">1080p (1920x1080)</option>\n                    <option value=\"2560x1440\">1440p (2560x1440)</option>\n                    <option value=\"3840x2160\">4K (3840x2160)</option>\n                  </select>\n                </div>\n\n                <div className=\"setting-item\">\n                  <label>Frame Rate:</label>\n                  <select\n                    value={tempSettings?.frameRate || 30}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      frameRate: parseInt(e.target.value)\n                    }))}\n                  >\n                    <option value={30}>30 FPS</option>\n                    <option value={60}>60 FPS</option>\n                  </select>\n                </div>\n\n                <div className=\"setting-item\">\n                  <label>Quality:</label>\n                  <select\n                    value={tempSettings?.quality || 'high'}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      quality: e.target.value\n                    }))}\n                  >\n                    <option value=\"draft\">Draft (Smaller file)</option>\n                    <option value=\"standard\">Standard</option>\n                    <option value=\"high\">High Quality</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"settings-group\">\n                <h4>Recording</h4>\n                <div className=\"setting-item\">\n                  <label>Countdown Timer (seconds):</label>\n                  <select\n                    value={tempSettings?.countdown || 0}\n                    onChange={(e) => setTempSettings(prev => ({\n                      ...prev,\n                      countdown: parseInt(e.target.value)\n                    }))}\n                  >\n                    <option value={0}>No countdown</option>\n                    <option value={3}>3 seconds</option>\n                    <option value={5}>5 seconds</option>\n                    <option value={10}>10 seconds</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"modal-footer\">\n              <button className=\"btn btn-secondary\" onClick={closeSettings}>\n                Cancel\n              </button>\n              <button className=\"btn btn-primary\" onClick={saveSettings}>\n                Save Settings\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <main className=\"content\">\n        <div className=\"source-selector\">\n          <h3>Select Screen or Window:</h3>\n          <div className=\"sources-grid\">\n            {sources.map((source) => (\n              <div\n                key={source.id}\n                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}\n                onClick={() => setSelectedSource(source)}\n              >\n                <img\n                  src={source.thumbnail}\n                  alt={source.name}\n                  className=\"source-thumbnail\"\n                />\n                <div className=\"source-name\">{source.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"recording-area\">\n          {recordedBlob ? (\n            <div className=\"preview-container\">\n              <video\n                ref={videoRef}\n                className=\"preview-video\"\n                controls\n                autoPlay={false}\n                preload=\"metadata\"\n                muted\n                playsInline\n              />\n              <div className=\"recording-info\">\n                <p>Recording completed • Duration: {formatDuration(recordingDuration)}</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"status-display\">\n              <div className={`status-box ${isRecording ? 'recording' : ''}`}>\n                {isRecording ? (\n                  <div className=\"recording-status\">\n                    <div className=\"recording-indicator\">\n                      <span className=\"recording-dot\"></span>\n                      RECORDING\n                    </div>\n                    <div className=\"recording-duration\">\n                      {formatDuration(recordingDuration)}\n                    </div>\n                    <div className=\"recording-source\">\n                      {selectedSource?.name}\n                    </div>\n                    <div className=\"recording-settings-info\">\n                      {recordingSettings.resolution} • {recordingSettings.frameRate}fps • {recordingSettings.quality}\n                    </div>\n\n                    {/* Audio level indicators */}\n                    {(recordingSettings.includeMicrophone || recordingSettings.includeSystemAudio) && (\n                      <div className=\"audio-levels\">\n                        {recordingSettings.includeMicrophone && (\n                          <div className=\"audio-level-item\">\n                            <span className=\"audio-label\">🎤</span>\n                            <div className=\"audio-meter\">\n                              <div\n                                className=\"audio-level-bar\"\n                                style={{ width: `${audioLevels.microphone * 100}%` }}\n                              />\n                            </div>\n                          </div>\n                        )}\n                        {recordingSettings.includeSystemAudio && (\n                          <div className=\"audio-level-item\">\n                            <span className=\"audio-label\">🔊</span>\n                            <div className=\"audio-meter\">\n                              <div\n                                className=\"audio-level-bar\"\n                                style={{ width: `${audioLevels.system * 100}%` }}\n                              />\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"ready-status\">\n                    <div className=\"status-text\">{status}</div>\n                    {selectedSource && (\n                      <div className=\"selected-source\">Ready to record: {selectedSource.name}</div>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              {/* Webcam preview during recording */}\n              {webcamStream && isRecording && (\n                <div className=\"webcam-preview\">\n                  <video\n                    ref={webcamRef}\n                    className=\"webcam-video\"\n                    autoPlay\n                    muted\n                    playsInline\n                  />\n                  <div className=\"webcam-label\">Webcam</div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n        \n        <div className=\"controls\">\n          {!isRecording && !recordedBlob && (\n            <button\n              className=\"btn btn-primary\"\n              onClick={startRecording}\n              disabled={!selectedSource || permissions.screen === 'denied'}\n            >\n              Start Recording\n            </button>\n          )}\n          \n          {isRecording && (\n            <button className=\"btn btn-danger\" onClick={stopRecording}>\n              Stop Recording\n            </button>\n          )}\n          \n          {recordedBlob && !isRecording && (\n            <>\n              {!showExportOptions ? (\n                <button \n                  className=\"btn btn-primary\" \n                  onClick={() => setShowExportOptions(true)}\n                >\n                  Download Recording\n                </button>\n              ) : (\n                <div className=\"export-options\">\n                  <h4>Choose format:</h4>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('webm')}\n                  >\n                    Download WebM (Fast)\n                  </button>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('mp4')}\n                  >\n                    Download MP4 (Compatible)\n                  </button>\n                  <button \n                    className=\"btn btn-secondary\" \n                    onClick={() => setShowExportOptions(false)}\n                  >\n                    Cancel\n                  </button>\n                </div>\n              )}\n              <button className=\"btn btn-secondary\" onClick={clearRecording}>\n                New Recording\n              </button>\n            </>\n          )}\n          \n          <button className=\"btn btn-secondary\" onClick={loadSources}>\n            Refresh Sources\n          </button>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,iBAAiB,CAAC;EACvD,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC;IACzD2B,kBAAkB,EAAE,KAAK;IACzBC,iBAAiB,EAAE,IAAI;IACvBC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,WAAW;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,MAAM;IACfC,kBAAkB,EAAE,SAAS;IAC7BC,cAAc,EAAE,SAAS;IACzBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC;IAAE4C,UAAU,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAC5E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC;IACvDkD,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAGpD,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMqD,SAAS,GAAGrD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMsD,gBAAgB,GAAGtD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMuD,SAAS,GAAGvD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMwD,mBAAmB,GAAGxD,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMyD,eAAe,GAAGzD,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM0D,WAAW,GAAG1D,MAAM,CAAC,IAAI,CAAC;EAEhCC,SAAS,CAAC,MAAM;IACd0D,gBAAgB,CAAC,CAAC;IAClBC,WAAW,CAAC,CAAC;IACbC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,OAAO,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,gBAAgB,CAAC,CAAC;MAE/D,MAAMhB,WAAW,GAAGa,OAAO,CACxBI,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC,CAC9CC,GAAG,CAACF,MAAM,KAAK;QACdG,EAAE,EAAEH,MAAM,CAACI,QAAQ;QACnBC,KAAK,EAAEL,MAAM,CAACK,KAAK,IAAI,cAAcL,MAAM,CAACI,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAClEC,OAAO,EAAEP,MAAM,CAACO;MAClB,CAAC,CAAC,CAAC;MAEL,MAAMxB,OAAO,GAAGY,OAAO,CACpBI,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC,CAC9CC,GAAG,CAACF,MAAM,KAAK;QACdG,EAAE,EAAEH,MAAM,CAACI,QAAQ;QACnBC,KAAK,EAAEL,MAAM,CAACK,KAAK,IAAI,UAAUL,MAAM,CAACI,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DC,OAAO,EAAEP,MAAM,CAACO;MAClB,CAAC,CAAC,CAAC;MAEL,MAAMvB,QAAQ,GAAGW,OAAO,CACrBI,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,aAAa,CAAC,CAC/CC,GAAG,CAACF,MAAM,KAAK;QACdG,EAAE,EAAEH,MAAM,CAACI,QAAQ;QACnBC,KAAK,EAAEL,MAAM,CAACK,KAAK,IAAI,WAAWL,MAAM,CAACI,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/DC,OAAO,EAAEP,MAAM,CAACO;MAClB,CAAC,CAAC,CAAC;MAEL1B,mBAAmB,CAAC;QAClBC,WAAW,EAAE,CACX;UAAEqB,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,oBAAoB;UAAEE,OAAO,EAAE;QAAK,CAAC,EAC7D,GAAGzB,WAAW,CACf;QACDC,OAAO,EAAE,CACP;UAAEoB,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,gBAAgB;UAAEE,OAAO,EAAE;QAAK,CAAC,EACzD,GAAGxB,OAAO,CACX;QACDC,QAAQ,EAAE,CACR;UAAEmB,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,iBAAiB;UAAEE,OAAO,EAAE;QAAK,CAAC,EAC1D,GAAGvB,QAAQ;MAEf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEF,KAAK,CAAC;MACnD;MACA3B,mBAAmB,CAAC;QAClBC,WAAW,EAAE,CAAC;UAAEqB,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,oBAAoB;UAAEE,OAAO,EAAE;QAAK,CAAC,CAAC;QAC5ExB,OAAO,EAAE,CAAC;UAAEoB,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,gBAAgB;UAAEE,OAAO,EAAE;QAAK,CAAC,CAAC;QACpEvB,QAAQ,EAAE,CAAC;UAAEmB,EAAE,EAAE,SAAS;UAAEE,KAAK,EAAE,iBAAiB;UAAEE,OAAO,EAAE;QAAK,CAAC;MACvE,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMf,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMmB,KAAK,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACrB,gBAAgB,CAAC,CAAC;IACzD1C,cAAc,CAAC6D,KAAK,CAAC;;IAErB;IACA,MAAMG,gBAAgB,GAAG,EAAE;IAC3B,IAAIH,KAAK,CAACI,MAAM,KAAK,SAAS,EAAED,gBAAgB,CAACE,IAAI,CAAC,oBAAoB,CAAC,CAAC,KACvEF,gBAAgB,CAACE,IAAI,CAAC,oBAAoB,CAAC;IAEhD,IAAIL,KAAK,CAACnC,UAAU,KAAK,SAAS,EAAEsC,gBAAgB,CAACE,IAAI,CAAC,cAAc,CAAC,CAAC,KACrEF,gBAAgB,CAACE,IAAI,CAAC,cAAc,CAAC;IAE1C,IAAIL,KAAK,CAACM,MAAM,KAAK,SAAS,EAAEH,gBAAgB,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC,KAC7DF,gBAAgB,CAACE,IAAI,CAAC,UAAU,CAAC;IAEtChE,SAAS,CAAC,gBAAgB8D,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;;IAExD;IACAC,UAAU,CAAC,MAAM;MACf,IAAI,CAAC9E,WAAW,IAAI,CAACM,YAAY,EAAE;QACjCK,SAAS,CAAC,iBAAiB,CAAC;MAC9B;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMoE,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,OAAO,GAAG,MAAMT,MAAM,CAACC,WAAW,CAACO,kBAAkB,CAAC,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACX,MAAM7B,gBAAgB,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMlD,OAAO,GAAG,MAAMqE,MAAM,CAACC,WAAW,CAACS,UAAU,CAAC,CAAC;IACrD9E,UAAU,CAACD,OAAO,CAAC;IACnB,IAAIA,OAAO,CAACgF,MAAM,GAAG,CAAC,IAAI,CAAC9E,cAAc,EAAE;MACzC;MACA,MAAMsE,MAAM,GAAGxE,OAAO,CAACiF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;MACxFjF,iBAAiB,CAACqE,MAAM,IAAIxE,OAAO,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMqF,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACnF,cAAc,EAAE;MACnBO,SAAS,CAAC,4CAA4C,CAAC;MACvD;IACF;IAEA,IAAI;MACF;MACA,IAAIkB,SAAS,GAAG,CAAC,EAAE;QACjB,MAAM2D,cAAc,CAAC,CAAC;MACxB;MAEA7E,SAAS,CAAC,uBAAuB,CAAC;MAClCE,oBAAoB,CAAC,CAAC,CAAC;;MAEvB;MACAmC,mBAAmB,CAACyC,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC9C7E,oBAAoB,CAAC8E,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG7E,iBAAiB,CAACK,UAAU,CAACyE,KAAK,CAAC,GAAG,CAAC,CAACjC,GAAG,CAACkC,MAAM,CAAC;;MAE3E;MACA,MAAMC,iBAAiB,GAAG;QACxBC,KAAK,EAAE,KAAK;QAAE;QACdC,KAAK,EAAE;UACLC,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAEjG,cAAc,CAAC0D,EAAE;YACtCwC,QAAQ,EAAEV,KAAK;YACfW,QAAQ,EAAEX,KAAK;YACfY,SAAS,EAAEX,MAAM;YACjBY,SAAS,EAAEZ,MAAM;YACjBa,YAAY,EAAE1F,iBAAiB,CAACM,SAAS;YACzCqF,YAAY,EAAE3F,iBAAiB,CAACM;UAClC;QACF;MACF,CAAC;MAED,MAAMsF,YAAY,GAAG,MAAMrD,SAAS,CAACC,YAAY,CAACqD,YAAY,CAACb,iBAAiB,CAAC;;MAEjF;MACA,IAAIc,gBAAgB,GAAG,IAAI;MAC3B,IAAI9F,iBAAiB,CAACG,iBAAiB,EAAE;QACvC,IAAI;UACF2F,gBAAgB,GAAG,MAAMvD,SAAS,CAACC,YAAY,CAACqD,YAAY,CAAC;YAC3DZ,KAAK,EAAE;cACLc,gBAAgB,EAAE,IAAI;cACtBC,gBAAgB,EAAE,IAAI;cACtBC,eAAe,EAAE,IAAI;cACrBC,UAAU,EAAE;YACd,CAAC;YACDhB,KAAK,EAAE;UACT,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOiB,UAAU,EAAE;UACnB/C,OAAO,CAACC,IAAI,CAAC,wDAAwD,CAAC;QACxE;MACF;;MAEA;MACA,IAAI+C,iBAAiB,GAAG,IAAI;MAC5B,IAAIpG,iBAAiB,CAACI,aAAa,EAAE;QACnC,IAAI;UACFgG,iBAAiB,GAAG,MAAM7D,SAAS,CAACC,YAAY,CAACqD,YAAY,CAAC;YAC5DX,KAAK,EAAE;cACLN,KAAK,EAAE;gBAAEyB,KAAK,EAAE;cAAI,CAAC;cACrBxB,MAAM,EAAE;gBAAEwB,KAAK,EAAE;cAAI,CAAC;cACtB/F,SAAS,EAAE;gBAAE+F,KAAK,EAAE;cAAG;YACzB,CAAC;YACDpB,KAAK,EAAE;UACT,CAAC,CAAC;UACFrE,eAAe,CAACwF,iBAAiB,CAAC;;UAElC;UACA,IAAIvE,SAAS,CAAC4C,OAAO,EAAE;YACrB5C,SAAS,CAAC4C,OAAO,CAAC6B,SAAS,GAAGF,iBAAiB;UACjD;QACF,CAAC,CAAC,OAAOG,WAAW,EAAE;UACpBnD,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;QAChE;MACF;;MAEA;MACA,IAAImD,cAAc,GAAGZ,YAAY;;MAEjC;MACA,IAAIE,gBAAgB,EAAE;QACpB,MAAMW,WAAW,GAAGX,gBAAgB,CAACY,cAAc,CAAC,CAAC;QACrDD,WAAW,CAACE,OAAO,CAACC,KAAK,IAAIJ,cAAc,CAACK,QAAQ,CAACD,KAAK,CAAC,CAAC;;QAE5D;QACAE,oBAAoB,CAAChB,gBAAgB,CAAC;MACxC;;MAEA;MACA;;MAEA/D,SAAS,CAAC0C,OAAO,GAAG+B,cAAc;;MAElC;MACA,MAAMO,QAAQ,GAAGC,kBAAkB,CAAC,CAAC;MACrC,MAAMC,aAAa,GAAG,IAAIC,aAAa,CAACV,cAAc,EAAE;QACtDO,QAAQ;QACRI,kBAAkB,EAAEC,eAAe,CAACpH,iBAAiB,CAACO,OAAO,EAAEqE,KAAK,EAAEC,MAAM;MAC9E,CAAC,CAAC;MAEF,MAAMwC,MAAM,GAAG,EAAE;MAEjBJ,aAAa,CAACK,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBJ,MAAM,CAAC1D,IAAI,CAAC4D,KAAK,CAACC,IAAI,CAAC;QACzB;MACF,CAAC;MAEDP,aAAa,CAACS,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACP,MAAM,EAAE;UAAEQ,IAAI,EAAEd;QAAS,CAAC,CAAC;QACjDxH,eAAe,CAACoI,IAAI,CAAC;QACrBhI,SAAS,CAAC,2CAA2C,CAAC;;QAEtD;QACA,IAAIyG,iBAAiB,EAAE;UACrBA,iBAAiB,CAAC0B,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;UAC5DnH,eAAe,CAAC,IAAI,CAAC;QACvB;;QAEA;QACAkD,UAAU,CAAC,MAAM;UACf,IAAIlC,QAAQ,CAAC6C,OAAO,IAAIkD,IAAI,EAAE;YAC5B,MAAMK,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;YAC1C/F,QAAQ,CAAC6C,OAAO,CAAC0D,GAAG,GAAGH,QAAQ;YAC/BpG,QAAQ,CAAC6C,OAAO,CAAC2D,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3B;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MAEDtG,gBAAgB,CAAC2C,OAAO,GAAGwC,aAAa;MACxCA,aAAa,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE1BpJ,cAAc,CAAC,IAAI,CAAC;MACpBU,SAAS,CAAC,uBAAuB,CAAC;IAEpC,CAAC,CAAC,OAAOwD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDxD,SAAS,CAAC,sDAAsD,CAAC;MACjE,IAAIqC,mBAAmB,CAACyC,OAAO,EAAE;QAC/B6D,aAAa,CAACtG,mBAAmB,CAACyC,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAM8D,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzG,gBAAgB,CAAC2C,OAAO,IAAIzF,WAAW,EAAE;MAC3C8C,gBAAgB,CAAC2C,OAAO,CAACsD,IAAI,CAAC,CAAC;;MAE/B;MACA,IAAI/F,mBAAmB,CAACyC,OAAO,EAAE;QAC/B6D,aAAa,CAACtG,mBAAmB,CAACyC,OAAO,CAAC;MAC5C;;MAEA;MACA,IAAI1C,SAAS,CAAC0C,OAAO,EAAE;QACrB1C,SAAS,CAAC0C,OAAO,CAACqD,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;MAC9D;;MAEA;MACA,IAAIpH,YAAY,EAAE;QAChBA,YAAY,CAACmH,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;QACvDnH,eAAe,CAAC,IAAI,CAAC;MACvB;;MAEA;MACA,IAAIqB,eAAe,CAACwC,OAAO,EAAE;QAC3BxC,eAAe,CAACwC,OAAO,CAAC+D,KAAK,CAAC,CAAC;QAC/BvG,eAAe,CAACwC,OAAO,GAAG,IAAI;QAC9BvC,WAAW,CAACuC,OAAO,GAAG,IAAI;MAC5B;;MAEA;MACAvD,cAAc,CAAC;QAAEC,UAAU,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,CAAC;MAE5CnC,cAAc,CAAC,KAAK,CAAC;MACrBU,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC;EAED,MAAM8I,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAMxE,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIyE,OAAO,CAAEC,OAAO,IAAK;MAC9B,IAAIC,KAAK,GAAGtI,SAAS;MACrBlB,SAAS,CAAC,uBAAuBwJ,KAAK,KAAK,CAAC;MAE5C,MAAMC,iBAAiB,GAAG1E,WAAW,CAAC,MAAM;QAC1CyE,KAAK,EAAE;QACP,IAAIA,KAAK,GAAG,CAAC,EAAE;UACbxJ,SAAS,CAAC,uBAAuBwJ,KAAK,KAAK,CAAC;QAC9C,CAAC,MAAM;UACLxJ,SAAS,CAAC,uBAAuB,CAAC;UAClC2I,aAAa,CAACc,iBAAiB,CAAC;UAChCF,OAAO,CAAC,CAAC;QACX;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMlC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMqC,KAAK,GAAG,CACZ,4BAA4B,EAC5B,4BAA4B,EAC5B,6BAA6B,EAC7B,YAAY,CACb;IAED,KAAK,MAAMxB,IAAI,IAAIwB,KAAK,EAAE;MACxB,IAAInC,aAAa,CAACoC,eAAe,CAACzB,IAAI,CAAC,EAAE;QACvC,OAAOA,IAAI;MACb;IACF;IACA,OAAO,YAAY;EACrB,CAAC;EAED,MAAMT,eAAe,GAAGA,CAAC7G,OAAO,EAAEqE,KAAK,EAAEC,MAAM,KAAK;IAClD,MAAM0E,UAAU,GAAG3E,KAAK,GAAGC,MAAM;IACjC,MAAM2E,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC,CAAC;;IAEpC,QAAQhJ,OAAO;MACb,KAAK,OAAO;QACV,OAAOqI,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI;MAAE;MAC5C,KAAK,UAAU;QACb,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;MAAE;MAC1C,KAAK,MAAM;QACT,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;MAAE;MAC1C;QACE,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;IAC1C;EACF,CAAC;EAED,MAAM1C,oBAAoB,GAAI2C,WAAW,IAAK;IAC5C,IAAI,CAACA,WAAW,EAAE;IAElB,IAAI;MACF,MAAMC,YAAY,GAAG,KAAKnG,MAAM,CAACoG,YAAY,IAAIpG,MAAM,CAACqG,kBAAkB,EAAE,CAAC;MAC7E,MAAMC,QAAQ,GAAGH,YAAY,CAACI,cAAc,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAGL,YAAY,CAACM,uBAAuB,CAACP,WAAW,CAAC;MAEhEI,QAAQ,CAACI,OAAO,GAAG,GAAG;MACtBF,MAAM,CAACG,OAAO,CAACL,QAAQ,CAAC;MAExB5H,eAAe,CAACwC,OAAO,GAAGiF,YAAY;MACtCxH,WAAW,CAACuC,OAAO,GAAGoF,QAAQ;;MAE9B;MACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAI,CAACjI,WAAW,CAACuC,OAAO,EAAE;QAE1B,MAAM2F,SAAS,GAAG,IAAIC,UAAU,CAACnI,WAAW,CAACuC,OAAO,CAAC6F,iBAAiB,CAAC;QACvEpI,WAAW,CAACuC,OAAO,CAAC8F,oBAAoB,CAACH,SAAS,CAAC;;QAEnD;QACA,MAAMI,OAAO,GAAGJ,SAAS,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGP,SAAS,CAAClG,MAAM;QACnF,MAAM0G,eAAe,GAAGhC,IAAI,CAACiC,GAAG,CAACL,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEpDtJ,cAAc,CAACyD,IAAI,KAAK;UACtB,GAAGA,IAAI;UACPxD,UAAU,EAAEyJ;QACd,CAAC,CAAC,CAAC;QAEH,IAAI5L,WAAW,EAAE;UACf8L,qBAAqB,CAACX,YAAY,CAAC;QACrC;MACF,CAAC;MAEDA,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOhH,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,gCAAgC,EAAEF,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAM4H,YAAY,GAAGA,CAAA,KAAM;IACzBzJ,eAAe,CAAC;MAAE,GAAGtB,iBAAiB;MAAEa;IAAU,CAAC,CAAC;IACpDG,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgK,aAAa,GAAGA,CAAA,KAAM;IAC1BhK,eAAe,CAAC,KAAK,CAAC;IACtBM,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM2J,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5J,YAAY,EAAE;MAChBpB,oBAAoB,CAAC;QACnBC,kBAAkB,EAAEmB,YAAY,CAACnB,kBAAkB;QACnDC,iBAAiB,EAAEkB,YAAY,CAAClB,iBAAiB;QACjDC,aAAa,EAAEiB,YAAY,CAACjB,aAAa;QACzCC,UAAU,EAAEgB,YAAY,CAAChB,UAAU;QACnCC,SAAS,EAAEe,YAAY,CAACf,SAAS;QACjCC,OAAO,EAAEc,YAAY,CAACd;MACxB,CAAC,CAAC;MACFO,YAAY,CAACO,YAAY,CAACR,SAAS,CAAC;IACtC;IACAG,eAAe,CAAC,KAAK,CAAC;IACtBM,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4J,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,GAAG,MAAM,KAAK;IACnD,IAAI7L,YAAY,EAAE;MAChB,IAAI;QACF,IAAI6L,MAAM,KAAK,MAAM,EAAE;UACrB;UACA,MAAMC,GAAG,GAAGnD,GAAG,CAACC,eAAe,CAAC5I,YAAY,CAAC;UAC7C,MAAM+L,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGJ,GAAG;UACZC,CAAC,CAACI,QAAQ,GAAG,aAAa,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC1I,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC2I,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UACzFP,CAAC,CAACQ,KAAK,CAAC,CAAC;UACT5D,GAAG,CAAC6D,eAAe,CAACV,GAAG,CAAC;UACxBrL,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,MAAM,IAAIoL,MAAM,KAAK,KAAK,EAAE;UAC3BxL,SAAS,CAAC,sBAAsB,CAAC;;UAEjC;UACA,MAAMoM,WAAW,GAAG,MAAMzM,YAAY,CAACyM,WAAW,CAAC,CAAC;UACpD,MAAMC,UAAU,GAAG,IAAI3B,UAAU,CAAC0B,WAAW,CAAC;;UAE9C;UACA,MAAME,QAAQ,GAAG,aAAa,IAAIP,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC1I,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC2I,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UAC7F,MAAMM,SAAS,GAAG,MAAM3I,MAAM,CAACC,WAAW,CAAC2I,aAAa,CAACH,UAAU,EAAEC,QAAQ,CAAC;UAE9E,IAAIC,SAAS,EAAE;YACbvM,SAAS,CAAC,yBAAyB,CAAC;YACpCI,oBAAoB,CAAC,KAAK,CAAC;UAC7B;QACF;MACF,CAAC,CAAC,OAAOoD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDxD,SAAS,CAAC,0BAA0B,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMyM,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAIxK,QAAQ,CAAC6C,OAAO,IAAI7C,QAAQ,CAAC6C,OAAO,CAAC0D,GAAG,EAAE;MAC5CF,GAAG,CAAC6D,eAAe,CAAClK,QAAQ,CAAC6C,OAAO,CAAC0D,GAAG,CAAC;MACzCvG,QAAQ,CAAC6C,OAAO,CAAC0D,GAAG,GAAG,EAAE;IAC3B;IACA5I,eAAe,CAAC,IAAI,CAAC;IACrBI,SAAS,CAAC,iBAAiB,CAAC;EAC9B,CAAC;EAED,oBACEhB,OAAA;IAAK0N,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB3N,OAAA;MAAQ0N,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACxB3N,OAAA;QAAA2N,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB/N,OAAA;QAAK0N,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3N,OAAA;UACE0N,SAAS,EAAC,mBAAmB;UAC7BM,OAAO,EAAExK,gBAAiB;UAAAmK,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/N,OAAA;UACE0N,SAAS,EAAC,mBAAmB;UAC7BM,OAAO,EAAE5B,YAAa;UACtB6B,QAAQ,EAAE5N,WAAY;UAAAsN,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRlN,WAAW,CAACkE,MAAM,KAAK,QAAQ,iBAC9B/E,OAAA;UAAQ0N,SAAS,EAAC,iBAAiB;UAACM,OAAO,EAAE5I,kBAAmB;UAAAuI,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAER3L,YAAY,iBACXpC,OAAA;MAAK0N,SAAS,EAAC,eAAe;MAACM,OAAO,EAAE3B,aAAc;MAAAsB,QAAA,eACpD3N,OAAA;QAAK0N,SAAS,EAAC,eAAe;QAACM,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;QAAAR,QAAA,gBACjE3N,OAAA;UAAK0N,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3N,OAAA;YAAA2N,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B/N,OAAA;YAAQ0N,SAAS,EAAC,aAAa;YAACM,OAAO,EAAE3B,aAAc;YAAAsB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAEN/N,OAAA;UAAK0N,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3N,OAAA;YAAK0N,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3N,OAAA;cAAA2N,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd/N,OAAA;cAAO0N,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC7B3N,OAAA;gBACEkJ,IAAI,EAAC,UAAU;gBACfkF,OAAO,EAAE,CAAA1L,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAElB,iBAAiB,KAAI,KAAM;gBAClD6M,QAAQ,EAAGH,CAAC,IAAKvL,eAAe,CAACqD,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPxE,iBAAiB,EAAE0M,CAAC,CAACI,MAAM,CAACF;gBAC9B,CAAC,CAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,sBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/N,OAAA;cAAO0N,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACtC3N,OAAA;gBACEkJ,IAAI,EAAC,UAAU;gBACfkF,OAAO,EAAE,KAAM;gBACfH,QAAQ,EAAE;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,wBAEF,eAAA/N,OAAA;gBAAM0N,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN/N,OAAA;YAAK0N,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3N,OAAA;cAAA2N,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd/N,OAAA;cAAO0N,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC7B3N,OAAA;gBACEkJ,IAAI,EAAC,UAAU;gBACfkF,OAAO,EAAE,CAAA1L,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEjB,aAAa,KAAI,KAAM;gBAC9C4M,QAAQ,EAAGH,CAAC,IAAKvL,eAAe,CAACqD,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPvE,aAAa,EAAEyM,CAAC,CAACI,MAAM,CAACF;gBAC1B,CAAC,CAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,kBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAER/N,OAAA;cAAK0N,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3N,OAAA;gBAAA2N,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B/N,OAAA;gBACEgM,KAAK,EAAE,CAAAtJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEhB,UAAU,KAAI,WAAY;gBAC/C2M,QAAQ,EAAGH,CAAC,IAAKvL,eAAe,CAACqD,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPtE,UAAU,EAAEwM,CAAC,CAACI,MAAM,CAACtC;gBACvB,CAAC,CAAC,CAAE;gBAAA2B,QAAA,gBAEJ3N,OAAA;kBAAQgM,KAAK,EAAC,UAAU;kBAAA2B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD/N,OAAA;kBAAQgM,KAAK,EAAC,WAAW;kBAAA2B,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD/N,OAAA;kBAAQgM,KAAK,EAAC,WAAW;kBAAA2B,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD/N,OAAA;kBAAQgM,KAAK,EAAC,WAAW;kBAAA2B,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/N,OAAA;cAAK0N,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3N,OAAA;gBAAA2N,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B/N,OAAA;gBACEgM,KAAK,EAAE,CAAAtJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEf,SAAS,KAAI,EAAG;gBACrC0M,QAAQ,EAAGH,CAAC,IAAKvL,eAAe,CAACqD,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPrE,SAAS,EAAE4M,QAAQ,CAACL,CAAC,CAACI,MAAM,CAACtC,KAAK;gBACpC,CAAC,CAAC,CAAE;gBAAA2B,QAAA,gBAEJ3N,OAAA;kBAAQgM,KAAK,EAAE,EAAG;kBAAA2B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC/N,OAAA;kBAAQgM,KAAK,EAAE,EAAG;kBAAA2B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/N,OAAA;cAAK0N,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3N,OAAA;gBAAA2N,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB/N,OAAA;gBACEgM,KAAK,EAAE,CAAAtJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEd,OAAO,KAAI,MAAO;gBACvCyM,QAAQ,EAAGH,CAAC,IAAKvL,eAAe,CAACqD,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACPpE,OAAO,EAAEsM,CAAC,CAACI,MAAM,CAACtC;gBACpB,CAAC,CAAC,CAAE;gBAAA2B,QAAA,gBAEJ3N,OAAA;kBAAQgM,KAAK,EAAC,OAAO;kBAAA2B,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnD/N,OAAA;kBAAQgM,KAAK,EAAC,UAAU;kBAAA2B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1C/N,OAAA;kBAAQgM,KAAK,EAAC,MAAM;kBAAA2B,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/N,OAAA;YAAK0N,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3N,OAAA;cAAA2N,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB/N,OAAA;cAAK0N,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3N,OAAA;gBAAA2N,QAAA,EAAO;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzC/N,OAAA;gBACEgM,KAAK,EAAE,CAAAtJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAER,SAAS,KAAI,CAAE;gBACpCmM,QAAQ,EAAGH,CAAC,IAAKvL,eAAe,CAACqD,IAAI,KAAK;kBACxC,GAAGA,IAAI;kBACP9D,SAAS,EAAEqM,QAAQ,CAACL,CAAC,CAACI,MAAM,CAACtC,KAAK;gBACpC,CAAC,CAAC,CAAE;gBAAA2B,QAAA,gBAEJ3N,OAAA;kBAAQgM,KAAK,EAAE,CAAE;kBAAA2B,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC/N,OAAA;kBAAQgM,KAAK,EAAE,CAAE;kBAAA2B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC/N,OAAA;kBAAQgM,KAAK,EAAE,CAAE;kBAAA2B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC/N,OAAA;kBAAQgM,KAAK,EAAE,EAAG;kBAAA2B,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/N,OAAA;UAAK0N,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3N,OAAA;YAAQ0N,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAE3B,aAAc;YAAAsB,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/N,OAAA;YAAQ0N,SAAS,EAAC,iBAAiB;YAACM,OAAO,EAAE1B,YAAa;YAAAqB,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED/N,OAAA;MAAM0N,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACvB3N,OAAA;QAAK0N,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3N,OAAA;UAAA2N,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjC/N,OAAA;UAAK0N,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BpN,OAAO,CAAC2D,GAAG,CAAEkH,MAAM,iBAClBpL,OAAA;YAEE0N,SAAS,EAAE,eAAe,CAAAjN,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0D,EAAE,MAAKiH,MAAM,CAACjH,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/E6J,OAAO,EAAEA,CAAA,KAAMtN,iBAAiB,CAAC0K,MAAM,CAAE;YAAAuC,QAAA,gBAEzC3N,OAAA;cACEwJ,GAAG,EAAE4B,MAAM,CAACoD,SAAU;cACtBC,GAAG,EAAErD,MAAM,CAAC1F,IAAK;cACjBgI,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACF/N,OAAA;cAAK0N,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEvC,MAAM,CAAC1F;YAAI;cAAAkI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAT3C3C,MAAM,CAACjH,EAAE;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/N,OAAA;QAAK0N,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BhN,YAAY,gBACXX,OAAA;UAAK0N,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3N,OAAA;YACE0O,GAAG,EAAEzL,QAAS;YACdyK,SAAS,EAAC,eAAe;YACzBiB,QAAQ;YACRC,QAAQ,EAAE,KAAM;YAChBC,OAAO,EAAC,UAAU;YAClBC,KAAK;YACLC,WAAW;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACF/N,OAAA;YAAK0N,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B3N,OAAA;cAAA2N,QAAA,GAAG,uCAAgC,EAAC7D,cAAc,CAAC7I,iBAAiB,CAAC;YAAA;cAAA2M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN/N,OAAA;UAAK0N,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3N,OAAA;YAAK0N,SAAS,EAAE,cAAcrN,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;YAAAsN,QAAA,EAC5DtN,WAAW,gBACVL,OAAA;cAAK0N,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B3N,OAAA;gBAAK0N,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC3N,OAAA;kBAAM0N,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,aAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN/N,OAAA;gBAAK0N,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAChC7D,cAAc,CAAC7I,iBAAiB;cAAC;gBAAA2M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACN/N,OAAA;gBAAK0N,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BlN,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiF;cAAI;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACN/N,OAAA;gBAAK0N,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GACrCtM,iBAAiB,CAACK,UAAU,EAAC,UAAG,EAACL,iBAAiB,CAACM,SAAS,EAAC,aAAM,EAACN,iBAAiB,CAACO,OAAO;cAAA;gBAAAgM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC,EAGL,CAAC1M,iBAAiB,CAACG,iBAAiB,IAAIH,iBAAiB,CAACE,kBAAkB,kBAC3EvB,OAAA;gBAAK0N,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC1BtM,iBAAiB,CAACG,iBAAiB,iBAClCxB,OAAA;kBAAK0N,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B3N,OAAA;oBAAM0N,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC/N,OAAA;oBAAK0N,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1B3N,OAAA;sBACE0N,SAAS,EAAC,iBAAiB;sBAC3BsB,KAAK,EAAE;wBAAE/I,KAAK,EAAE,GAAG3D,WAAW,CAACE,UAAU,GAAG,GAAG;sBAAI;oBAAE;sBAAAoL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EACA1M,iBAAiB,CAACE,kBAAkB,iBACnCvB,OAAA;kBAAK0N,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B3N,OAAA;oBAAM0N,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC/N,OAAA;oBAAK0N,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1B3N,OAAA;sBACE0N,SAAS,EAAC,iBAAiB;sBAC3BsB,KAAK,EAAE;wBAAE/I,KAAK,EAAE,GAAG3D,WAAW,CAACG,MAAM,GAAG,GAAG;sBAAI;oBAAE;sBAAAmL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEN/N,OAAA;cAAK0N,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3N,OAAA;gBAAK0N,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE5M;cAAM;gBAAA6M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1CtN,cAAc,iBACbT,OAAA;gBAAK0N,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,mBAAiB,EAAClN,cAAc,CAACiF,IAAI;cAAA;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL/L,YAAY,IAAI3B,WAAW,iBAC1BL,OAAA;YAAK0N,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3N,OAAA;cACE0O,GAAG,EAAExL,SAAU;cACfwK,SAAS,EAAC,cAAc;cACxBkB,QAAQ;cACRE,KAAK;cACLC,WAAW;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACF/N,OAAA;cAAK0N,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/N,OAAA;QAAK0N,SAAS,EAAC,UAAU;QAAAC,QAAA,GACtB,CAACtN,WAAW,IAAI,CAACM,YAAY,iBAC5BX,OAAA;UACE0N,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEpI,cAAe;UACxBqI,QAAQ,EAAE,CAACxN,cAAc,IAAII,WAAW,CAACkE,MAAM,KAAK,QAAS;UAAA4I,QAAA,EAC9D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEA1N,WAAW,iBACVL,OAAA;UAAQ0N,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAEpE,aAAc;UAAA+D,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEApN,YAAY,IAAI,CAACN,WAAW,iBAC3BL,OAAA,CAAAE,SAAA;UAAAyN,QAAA,GACG,CAACxM,iBAAiB,gBACjBnB,OAAA;YACE0N,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAM5M,oBAAoB,CAAC,IAAI,CAAE;YAAAuM,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET/N,OAAA;YAAK0N,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3N,OAAA;cAAA2N,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvB/N,OAAA;cACE0N,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAAC,MAAM,CAAE;cAAAoB,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/N,OAAA;cACE0N,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAAC,KAAK,CAAE;cAAAoB,QAAA,EACzC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/N,OAAA;cACE0N,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAEA,CAAA,KAAM5M,oBAAoB,CAAC,KAAK,CAAE;cAAAuM,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACD/N,OAAA;YAAQ0N,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAEP,cAAe;YAAAE,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,eAED/N,OAAA;UAAQ0N,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAEvK,WAAY;UAAAkK,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC3N,EAAA,CAtzBQD,GAAG;AAAA8O,EAAA,GAAH9O,GAAG;AAwzBZ,eAAeA,GAAG;AAAC,IAAA8O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}