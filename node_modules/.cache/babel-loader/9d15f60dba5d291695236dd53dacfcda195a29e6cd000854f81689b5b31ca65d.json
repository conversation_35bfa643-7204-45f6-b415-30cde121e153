{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js\",\n  _s = $RefreshSig$();\n/// <reference path=\"./electron.d.ts\" />\nimport { useState, useRef, useEffect } from 'react';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n\n  // New Phase 2 state variables\n  const [recordingSettings, setRecordingSettings] = useState({\n    includeSystemAudio: false,\n    includeMicrophone: true,\n    includeWebcam: false,\n    resolution: '1920x1080',\n    frameRate: 30,\n    quality: 'high'\n  });\n  const [webcamStream, setWebcamStream] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [audioLevels, setAudioLevels] = useState({\n    microphone: 0,\n    system: 0\n  });\n  const videoRef = useRef(null);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n    try {\n      // Start countdown if enabled\n      if (countdown > 0) {\n        await startCountdown();\n      }\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Parse resolution settings\n      const [width, height] = recordingSettings.resolution.split('x').map(Number);\n\n      // Get screen stream with enhanced settings\n      const screenConstraints = {\n        audio: recordingSettings.includeSystemAudio ? {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id\n          }\n        } : false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: width,\n            maxWidth: width,\n            minHeight: height,\n            maxHeight: height,\n            minFrameRate: recordingSettings.frameRate,\n            maxFrameRate: recordingSettings.frameRate\n          }\n        }\n      };\n      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);\n\n      // Get microphone stream if enabled\n      let microphoneStream = null;\n      if (recordingSettings.includeMicrophone) {\n        try {\n          microphoneStream = await navigator.mediaDevices.getUserMedia({\n            audio: {\n              echoCancellation: true,\n              noiseSuppression: true,\n              autoGainControl: true,\n              sampleRate: 48000\n            },\n            video: false\n          });\n        } catch (audioError) {\n          console.warn('Microphone access denied, recording without microphone');\n        }\n      }\n\n      // Get webcam stream if enabled\n      let webcamStreamLocal = null;\n      if (recordingSettings.includeWebcam) {\n        try {\n          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({\n            video: {\n              width: {\n                ideal: 320\n              },\n              height: {\n                ideal: 240\n              },\n              frameRate: {\n                ideal: 30\n              }\n            },\n            audio: false\n          });\n          setWebcamStream(webcamStreamLocal);\n\n          // Display webcam preview\n          if (webcamRef.current) {\n            webcamRef.current.srcObject = webcamStreamLocal;\n          }\n        } catch (webcamError) {\n          console.warn('Webcam access denied, recording without webcam');\n        }\n      }\n\n      // Combine all streams\n      let combinedStream = screenStream;\n\n      // Add microphone audio if available\n      if (microphoneStream) {\n        const audioTracks = microphoneStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n      }\n\n      // Note: Webcam video will be composited in post-processing for now\n      // In a future version, we could use Canvas API to composite in real-time\n\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder with quality settings\n      const mimeType = getOptimalMimeType();\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType,\n        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)\n      });\n      const chunks = [];\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, {\n          type: mimeType\n        });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Clean up webcam stream\n        if (webcamStreamLocal) {\n          webcamStreamLocal.getTracks().forEach(track => track.stop());\n          setWebcamStream(null);\n        }\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n\n      // Stop webcam stream\n      if (webcamStream) {\n        webcamStream.getTracks().forEach(track => track.stop());\n        setWebcamStream(null);\n      }\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Helper functions for Phase 2 features\n  const startCountdown = () => {\n    return new Promise(resolve => {\n      let count = countdown;\n      setStatus(`Recording starts in ${count}...`);\n      const countdownInterval = setInterval(() => {\n        count--;\n        if (count > 0) {\n          setStatus(`Recording starts in ${count}...`);\n        } else {\n          setStatus('Recording starting...');\n          clearInterval(countdownInterval);\n          resolve();\n        }\n      }, 1000);\n    });\n  };\n  const getOptimalMimeType = () => {\n    const types = ['video/webm;codecs=vp9,opus', 'video/webm;codecs=vp8,opus', 'video/webm;codecs=h264,opus', 'video/webm'];\n    for (const type of types) {\n      if (MediaRecorder.isTypeSupported(type)) {\n        return type;\n      }\n    }\n    return 'video/webm';\n  };\n  const getVideoBitrate = (quality, width, height) => {\n    const pixelCount = width * height;\n    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels\n\n    switch (quality) {\n      case 'draft':\n        return Math.floor(baseRate * 0.5) * 1000;\n      // 0.5 bits per pixel\n      case 'standard':\n        return Math.floor(baseRate * 1) * 1000;\n      // 1 bit per pixel\n      case 'high':\n        return Math.floor(baseRate * 2) * 1000;\n      // 2 bits per pixel\n      default:\n        return Math.floor(baseRate * 1) * 1000;\n    }\n  };\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Screen Recorder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => setShowSettings(!showSettings),\n          children: \"\\u2699\\uFE0F Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this), permissions.screen === 'denied' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: requestPermissions,\n          children: \"Grant Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Recording Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Audio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: recordingSettings.includeMicrophone,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              includeMicrophone: e.target.checked\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this), \"Include Microphone\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: recordingSettings.includeSystemAudio,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              includeSystemAudio: e.target.checked\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), \"Include System Audio\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Video\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: recordingSettings.includeWebcam,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              includeWebcam: e.target.checked\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this), \"Include Webcam\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Resolution:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: recordingSettings.resolution,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              resolution: e.target.value\n            })),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"1280x720\",\n              children: \"720p (1280x720)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"1920x1080\",\n              children: \"1080p (1920x1080)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"2560x1440\",\n              children: \"1440p (2560x1440)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"3840x2160\",\n              children: \"4K (3840x2160)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Frame Rate:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: recordingSettings.frameRate,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              frameRate: parseInt(e.target.value)\n            })),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: 30,\n              children: \"30 FPS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 60,\n              children: \"60 FPS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Quality:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: recordingSettings.quality,\n            onChange: e => setRecordingSettings(prev => ({\n              ...prev,\n              quality: e.target.value\n            })),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"draft\",\n              children: \"Draft (Smaller file)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"standard\",\n              children: \"Standard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"high\",\n              children: \"High Quality\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Countdown Timer (seconds):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: countdown,\n            onChange: e => setCountdown(parseInt(e.target.value)),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: 0,\n              children: \"No countdown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 3,\n              children: \"3 seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 5,\n              children: \"5 seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 10,\n              children: \"10 seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"source-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select Screen or Window:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sources-grid\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `source-item ${(selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.id) === source.id ? 'selected' : ''}`,\n            onClick: () => setSelectedSource(source),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: source.thumbnail,\n              alt: source.name,\n              className: \"source-thumbnail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"source-name\",\n              children: source.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this)]\n          }, source.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-area\",\n        children: recordedBlob ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: videoRef,\n            className: \"preview-video\",\n            controls: true,\n            autoPlay: false,\n            preload: \"metadata\",\n            muted: true,\n            playsInline: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recording-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Recording completed \\u2022 Duration: \", formatDuration(recordingDuration)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status-box ${isRecording ? 'recording' : ''}`,\n            children: isRecording ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"recording-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"recording-dot\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 23\n                }, this), \"RECORDING\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-duration\",\n                children: formatDuration(recordingDuration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-source\",\n                children: selectedSource === null || selectedSource === void 0 ? void 0 : selectedSource.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recording-settings-info\",\n                children: [recordingSettings.resolution, \" \\u2022 \", recordingSettings.frameRate, \"fps \\u2022 \", recordingSettings.quality]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ready-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-text\",\n                children: status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 21\n              }, this), selectedSource && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-source\",\n                children: [\"Ready to record: \", selectedSource.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this), webcamStream && isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webcam-preview\",\n            children: [/*#__PURE__*/_jsxDEV(\"video\", {\n              ref: webcamRef,\n              className: \"webcam-video\",\n              autoPlay: true,\n              muted: true,\n              playsInline: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"webcam-label\",\n              children: \"Webcam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        children: [!isRecording && !recordedBlob && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: startRecording,\n          disabled: !selectedSource || permissions.screen === 'denied',\n          children: \"Start Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 13\n        }, this), isRecording && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-danger\",\n          onClick: stopRecording,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this), recordedBlob && !isRecording && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [!showExportOptions ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowExportOptions(true),\n            children: \"Download Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"export-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Choose format:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('webm'),\n              children: \"Download WebM (Fast)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => downloadRecording('mp4'),\n              children: \"Download MP4 (Compatible)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => setShowExportOptions(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearRecording,\n            children: \"New Recording\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: loadSources,\n          children: \"Refresh Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn\",\n          style: {\n            background: 'purple',\n            color: 'white'\n          },\n          onClick: () => alert('TEST: Code changes are working!'),\n          children: \"TEST BUTTON\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 343,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"U1uSeYOTqI9OFCzN1Z4pu9DbXj0=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isRecording", "setIsRecording", "sources", "setSources", "selectedSource", "setSelectedSource", "recordedBlob", "setRecordedBlob", "permissions", "setPermissions", "status", "setStatus", "recordingDuration", "setRecordingDuration", "showExportOptions", "setShowExportOptions", "recordingSettings", "setRecordingSettings", "includeSystemAudio", "includeMicrophone", "includeWebcam", "resolution", "frameRate", "quality", "webcamStream", "setWebcamStream", "countdown", "setCountdown", "showSettings", "setShowSettings", "audioLevels", "setAudioLevels", "microphone", "system", "videoRef", "webcamRef", "mediaRecorderRef", "streamRef", "durationIntervalRef", "checkPermissions", "loadSources", "perms", "window", "electronAPI", "requestPermissions", "granted", "getSources", "length", "screen", "find", "s", "name", "includes", "startRecording", "startCountdown", "current", "setInterval", "prev", "width", "height", "split", "map", "Number", "screenConstraints", "audio", "mandatory", "chromeMediaSource", "chromeMediaSourceId", "id", "video", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "minFrameRate", "maxFrameRate", "screenStream", "navigator", "mediaDevices", "getUserMedia", "microphoneStream", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "audioError", "console", "warn", "webcamStreamLocal", "ideal", "srcObject", "webcamError", "combinedStream", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "addTrack", "mimeType", "getOptimalMimeType", "mediaRecorder", "MediaRecorder", "videoBitsPerSecond", "getVideoBitrate", "chunks", "ondataavailable", "event", "data", "size", "push", "onstop", "blob", "Blob", "type", "getTracks", "stop", "setTimeout", "videoUrl", "URL", "createObjectURL", "src", "load", "start", "error", "clearInterval", "stopRecording", "formatDuration", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "Promise", "resolve", "count", "countdownInterval", "types", "isTypeSupported", "pixelCount", "baseRate", "downloadRecording", "format", "url", "a", "document", "createElement", "href", "download", "Date", "toISOString", "slice", "replace", "click", "revokeObjectURL", "arrayBuffer", "uint8Array", "Uint8Array", "filename", "savedPath", "saveRecording", "clearRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "checked", "onChange", "e", "target", "value", "parseInt", "source", "thumbnail", "alt", "ref", "controls", "autoPlay", "preload", "muted", "playsInline", "disabled", "style", "background", "color", "alert", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/micro-startups/rails-work/screen-recorder/src/App.js"], "sourcesContent": ["/// <reference path=\"./electron.d.ts\" />\nimport { useState, useRef, useEffect } from 'react';\nimport './index.css';\n\nfunction App() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [sources, setSources] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(null);\n  const [recordedBlob, setRecordedBlob] = useState(null);\n  const [permissions, setPermissions] = useState({});\n  const [status, setStatus] = useState('Ready to record');\n  const [recordingDuration, setRecordingDuration] = useState(0);\n  const [showExportOptions, setShowExportOptions] = useState(false);\n\n  // New Phase 2 state variables\n  const [recordingSettings, setRecordingSettings] = useState({\n    includeSystemAudio: false,\n    includeMicrophone: true,\n    includeWebcam: false,\n    resolution: '1920x1080',\n    frameRate: 30,\n    quality: 'high'\n  });\n  const [webcamStream, setWebcamStream] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [audioLevels, setAudioLevels] = useState({ microphone: 0, system: 0 });\n\n  const videoRef = useRef(null);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const streamRef = useRef(null);\n  const durationIntervalRef = useRef(null);\n  \n  useEffect(() => {\n    checkPermissions();\n    loadSources();\n  }, []);\n  \n  const checkPermissions = async () => {\n    const perms = await window.electronAPI.checkPermissions();\n    setPermissions(perms);\n  };\n  \n  const requestPermissions = async () => {\n    const granted = await window.electronAPI.requestPermissions();\n    if (granted) {\n      await checkPermissions();\n    }\n  };\n  \n  const loadSources = async () => {\n    const sources = await window.electronAPI.getSources();\n    setSources(sources);\n    if (sources.length > 0 && !selectedSource) {\n      // Auto-select the first screen\n      const screen = sources.find(s => s.name.includes('Screen') || s.name.includes('Entire'));\n      setSelectedSource(screen || sources[0]);\n    }\n  };\n  \n  const startRecording = async () => {\n    if (!selectedSource) {\n      setStatus('Please select a screen or window to record');\n      return;\n    }\n\n    try {\n      // Start countdown if enabled\n      if (countdown > 0) {\n        await startCountdown();\n      }\n\n      setStatus('Starting recording...');\n      setRecordingDuration(0);\n\n      // Start duration timer\n      durationIntervalRef.current = setInterval(() => {\n        setRecordingDuration(prev => prev + 1);\n      }, 1000);\n\n      // Parse resolution settings\n      const [width, height] = recordingSettings.resolution.split('x').map(Number);\n\n      // Get screen stream with enhanced settings\n      const screenConstraints = {\n        audio: recordingSettings.includeSystemAudio ? {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id\n          }\n        } : false,\n        video: {\n          mandatory: {\n            chromeMediaSource: 'desktop',\n            chromeMediaSourceId: selectedSource.id,\n            minWidth: width,\n            maxWidth: width,\n            minHeight: height,\n            maxHeight: height,\n            minFrameRate: recordingSettings.frameRate,\n            maxFrameRate: recordingSettings.frameRate\n          }\n        }\n      };\n\n      const screenStream = await navigator.mediaDevices.getUserMedia(screenConstraints);\n\n      // Get microphone stream if enabled\n      let microphoneStream = null;\n      if (recordingSettings.includeMicrophone) {\n        try {\n          microphoneStream = await navigator.mediaDevices.getUserMedia({\n            audio: {\n              echoCancellation: true,\n              noiseSuppression: true,\n              autoGainControl: true,\n              sampleRate: 48000\n            },\n            video: false\n          });\n        } catch (audioError) {\n          console.warn('Microphone access denied, recording without microphone');\n        }\n      }\n\n      // Get webcam stream if enabled\n      let webcamStreamLocal = null;\n      if (recordingSettings.includeWebcam) {\n        try {\n          webcamStreamLocal = await navigator.mediaDevices.getUserMedia({\n            video: {\n              width: { ideal: 320 },\n              height: { ideal: 240 },\n              frameRate: { ideal: 30 }\n            },\n            audio: false\n          });\n          setWebcamStream(webcamStreamLocal);\n\n          // Display webcam preview\n          if (webcamRef.current) {\n            webcamRef.current.srcObject = webcamStreamLocal;\n          }\n        } catch (webcamError) {\n          console.warn('Webcam access denied, recording without webcam');\n        }\n      }\n\n      // Combine all streams\n      let combinedStream = screenStream;\n\n      // Add microphone audio if available\n      if (microphoneStream) {\n        const audioTracks = microphoneStream.getAudioTracks();\n        audioTracks.forEach(track => combinedStream.addTrack(track));\n      }\n\n      // Note: Webcam video will be composited in post-processing for now\n      // In a future version, we could use Canvas API to composite in real-time\n\n      streamRef.current = combinedStream;\n\n      // Set up MediaRecorder with quality settings\n      const mimeType = getOptimalMimeType();\n      const mediaRecorder = new MediaRecorder(combinedStream, {\n        mimeType,\n        videoBitsPerSecond: getVideoBitrate(recordingSettings.quality, width, height)\n      });\n\n      const chunks = [];\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunks.push(event.data);\n        }\n      };\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, { type: mimeType });\n        setRecordedBlob(blob);\n        setStatus('Recording saved. You can now download it.');\n\n        // Clean up webcam stream\n        if (webcamStreamLocal) {\n          webcamStreamLocal.getTracks().forEach(track => track.stop());\n          setWebcamStream(null);\n        }\n\n        // Show preview - use setTimeout to ensure the video element is rendered\n        setTimeout(() => {\n          if (videoRef.current && blob) {\n            const videoUrl = URL.createObjectURL(blob);\n            videoRef.current.src = videoUrl;\n            videoRef.current.load(); // Force reload of the video element\n          }\n        }, 100);\n      };\n\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n\n      setIsRecording(true);\n      setStatus('Recording in progress');\n\n    } catch (error) {\n      console.error('Failed to start recording:', error);\n      setStatus('Failed to start recording. Please check permissions.');\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n    }\n  };\n  \n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n\n      // Stop duration timer\n      if (durationIntervalRef.current) {\n        clearInterval(durationIntervalRef.current);\n      }\n\n      // Stop all tracks\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n      }\n\n      // Stop webcam stream\n      if (webcamStream) {\n        webcamStream.getTracks().forEach(track => track.stop());\n        setWebcamStream(null);\n      }\n\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  };\n  \n  const formatDuration = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Helper functions for Phase 2 features\n  const startCountdown = () => {\n    return new Promise((resolve) => {\n      let count = countdown;\n      setStatus(`Recording starts in ${count}...`);\n\n      const countdownInterval = setInterval(() => {\n        count--;\n        if (count > 0) {\n          setStatus(`Recording starts in ${count}...`);\n        } else {\n          setStatus('Recording starting...');\n          clearInterval(countdownInterval);\n          resolve();\n        }\n      }, 1000);\n    });\n  };\n\n  const getOptimalMimeType = () => {\n    const types = [\n      'video/webm;codecs=vp9,opus',\n      'video/webm;codecs=vp8,opus',\n      'video/webm;codecs=h264,opus',\n      'video/webm'\n    ];\n\n    for (const type of types) {\n      if (MediaRecorder.isTypeSupported(type)) {\n        return type;\n      }\n    }\n    return 'video/webm';\n  };\n\n  const getVideoBitrate = (quality, width, height) => {\n    const pixelCount = width * height;\n    const baseRate = pixelCount / 1000; // Base rate per 1000 pixels\n\n    switch (quality) {\n      case 'draft':\n        return Math.floor(baseRate * 0.5) * 1000; // 0.5 bits per pixel\n      case 'standard':\n        return Math.floor(baseRate * 1) * 1000; // 1 bit per pixel\n      case 'high':\n        return Math.floor(baseRate * 2) * 1000; // 2 bits per pixel\n      default:\n        return Math.floor(baseRate * 1) * 1000;\n    }\n  };\n\n  const downloadRecording = async (format = 'webm') => {\n    if (recordedBlob) {\n      try {\n        if (format === 'webm') {\n          // Direct WebM download\n          const url = URL.createObjectURL(recordedBlob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          a.click();\n          URL.revokeObjectURL(url);\n          setShowExportOptions(false);\n        } else if (format === 'mp4') {\n          setStatus('Converting to MP4...');\n\n          // Convert blob to array buffer (browser-compatible)\n          const arrayBuffer = await recordedBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Save and convert via Electron\n          const filename = `recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;\n          const savedPath = await window.electronAPI.saveRecording(uint8Array, filename);\n\n          if (savedPath) {\n            setStatus('Recording saved as MP4!');\n            setShowExportOptions(false);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to save recording:', error);\n        setStatus('Failed to save recording');\n      }\n    }\n  };\n  \n  const clearRecording = () => {\n    // Clean up blob URL to prevent memory leaks\n    if (videoRef.current && videoRef.current.src) {\n      URL.revokeObjectURL(videoRef.current.src);\n      videoRef.current.src = '';\n    }\n    setRecordedBlob(null);\n    setStatus('Ready to record');\n  };\n  \n  return (\n    <div className=\"app\">\n      <header className=\"header\">\n        <h1>Screen Recorder</h1>\n        <div className=\"header-controls\">\n          <button\n            className=\"btn btn-secondary\"\n            onClick={() => setShowSettings(!showSettings)}\n          >\n            ⚙️ Settings\n          </button>\n          {permissions.screen === 'denied' && (\n            <button className=\"btn btn-secondary\" onClick={requestPermissions}>\n              Grant Permissions\n            </button>\n          )}\n        </div>\n      </header>\n\n      {showSettings && (\n        <div className=\"settings-panel\">\n          <h3>Recording Settings</h3>\n\n          <div className=\"settings-group\">\n            <h4>Audio</h4>\n            <label className=\"setting-item\">\n              <input\n                type=\"checkbox\"\n                checked={recordingSettings.includeMicrophone}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  includeMicrophone: e.target.checked\n                }))}\n              />\n              Include Microphone\n            </label>\n            <label className=\"setting-item\">\n              <input\n                type=\"checkbox\"\n                checked={recordingSettings.includeSystemAudio}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  includeSystemAudio: e.target.checked\n                }))}\n              />\n              Include System Audio\n            </label>\n          </div>\n\n          <div className=\"settings-group\">\n            <h4>Video</h4>\n            <label className=\"setting-item\">\n              <input\n                type=\"checkbox\"\n                checked={recordingSettings.includeWebcam}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  includeWebcam: e.target.checked\n                }))}\n              />\n              Include Webcam\n            </label>\n\n            <div className=\"setting-item\">\n              <label>Resolution:</label>\n              <select\n                value={recordingSettings.resolution}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  resolution: e.target.value\n                }))}\n              >\n                <option value=\"1280x720\">720p (1280x720)</option>\n                <option value=\"1920x1080\">1080p (1920x1080)</option>\n                <option value=\"2560x1440\">1440p (2560x1440)</option>\n                <option value=\"3840x2160\">4K (3840x2160)</option>\n              </select>\n            </div>\n\n            <div className=\"setting-item\">\n              <label>Frame Rate:</label>\n              <select\n                value={recordingSettings.frameRate}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  frameRate: parseInt(e.target.value)\n                }))}\n              >\n                <option value={30}>30 FPS</option>\n                <option value={60}>60 FPS</option>\n              </select>\n            </div>\n\n            <div className=\"setting-item\">\n              <label>Quality:</label>\n              <select\n                value={recordingSettings.quality}\n                onChange={(e) => setRecordingSettings(prev => ({\n                  ...prev,\n                  quality: e.target.value\n                }))}\n              >\n                <option value=\"draft\">Draft (Smaller file)</option>\n                <option value=\"standard\">Standard</option>\n                <option value=\"high\">High Quality</option>\n              </select>\n            </div>\n          </div>\n\n          <div className=\"settings-group\">\n            <h4>Recording</h4>\n            <div className=\"setting-item\">\n              <label>Countdown Timer (seconds):</label>\n              <select\n                value={countdown}\n                onChange={(e) => setCountdown(parseInt(e.target.value))}\n              >\n                <option value={0}>No countdown</option>\n                <option value={3}>3 seconds</option>\n                <option value={5}>5 seconds</option>\n                <option value={10}>10 seconds</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <main className=\"content\">\n        <div className=\"source-selector\">\n          <h3>Select Screen or Window:</h3>\n          <div className=\"sources-grid\">\n            {sources.map((source) => (\n              <div\n                key={source.id}\n                className={`source-item ${selectedSource?.id === source.id ? 'selected' : ''}`}\n                onClick={() => setSelectedSource(source)}\n              >\n                <img\n                  src={source.thumbnail}\n                  alt={source.name}\n                  className=\"source-thumbnail\"\n                />\n                <div className=\"source-name\">{source.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"recording-area\">\n          {recordedBlob ? (\n            <div className=\"preview-container\">\n              <video\n                ref={videoRef}\n                className=\"preview-video\"\n                controls\n                autoPlay={false}\n                preload=\"metadata\"\n                muted\n                playsInline\n              />\n              <div className=\"recording-info\">\n                <p>Recording completed • Duration: {formatDuration(recordingDuration)}</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"status-display\">\n              <div className={`status-box ${isRecording ? 'recording' : ''}`}>\n                {isRecording ? (\n                  <div className=\"recording-status\">\n                    <div className=\"recording-indicator\">\n                      <span className=\"recording-dot\"></span>\n                      RECORDING\n                    </div>\n                    <div className=\"recording-duration\">\n                      {formatDuration(recordingDuration)}\n                    </div>\n                    <div className=\"recording-source\">\n                      {selectedSource?.name}\n                    </div>\n                    <div className=\"recording-settings-info\">\n                      {recordingSettings.resolution} • {recordingSettings.frameRate}fps • {recordingSettings.quality}\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"ready-status\">\n                    <div className=\"status-text\">{status}</div>\n                    {selectedSource && (\n                      <div className=\"selected-source\">Ready to record: {selectedSource.name}</div>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              {/* Webcam preview during recording */}\n              {webcamStream && isRecording && (\n                <div className=\"webcam-preview\">\n                  <video\n                    ref={webcamRef}\n                    className=\"webcam-video\"\n                    autoPlay\n                    muted\n                    playsInline\n                  />\n                  <div className=\"webcam-label\">Webcam</div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n        \n        <div className=\"controls\">\n          {!isRecording && !recordedBlob && (\n            <button\n              className=\"btn btn-primary\"\n              onClick={startRecording}\n              disabled={!selectedSource || permissions.screen === 'denied'}\n            >\n              Start Recording\n            </button>\n          )}\n          \n          {isRecording && (\n            <button className=\"btn btn-danger\" onClick={stopRecording}>\n              Stop Recording\n            </button>\n          )}\n          \n          {recordedBlob && !isRecording && (\n            <>\n              {!showExportOptions ? (\n                <button \n                  className=\"btn btn-primary\" \n                  onClick={() => setShowExportOptions(true)}\n                >\n                  Download Recording\n                </button>\n              ) : (\n                <div className=\"export-options\">\n                  <h4>Choose format:</h4>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('webm')}\n                  >\n                    Download WebM (Fast)\n                  </button>\n                  <button \n                    className=\"btn btn-primary\" \n                    onClick={() => downloadRecording('mp4')}\n                  >\n                    Download MP4 (Compatible)\n                  </button>\n                  <button \n                    className=\"btn btn-secondary\" \n                    onClick={() => setShowExportOptions(false)}\n                  >\n                    Cancel\n                  </button>\n                </div>\n              )}\n              <button className=\"btn btn-secondary\" onClick={clearRecording}>\n                New Recording\n              </button>\n            </>\n          )}\n          \n          <button className=\"btn btn-secondary\" onClick={loadSources}>\n            Refresh Sources\n          </button>\n          \n          <button \n            className=\"btn\" \n            style={{background: 'purple', color: 'white'}} \n            onClick={() => alert('TEST: Code changes are working!')}\n          >\n            TEST BUTTON\n          </button>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,iBAAiB,CAAC;EACvD,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC;IACzD2B,kBAAkB,EAAE,KAAK;IACzBC,iBAAiB,EAAE,IAAI;IACvBC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,WAAW;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,UAAU,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAE5E,MAAMC,QAAQ,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM2C,SAAS,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4C,gBAAgB,GAAG5C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM6C,SAAS,GAAG7C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM8C,mBAAmB,GAAG9C,MAAM,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd8C,gBAAgB,CAAC,CAAC;IAClBC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAME,KAAK,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACJ,gBAAgB,CAAC,CAAC;IACzD9B,cAAc,CAACgC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,OAAO,GAAG,MAAMH,MAAM,CAACC,WAAW,CAACC,kBAAkB,CAAC,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACX,MAAMN,gBAAgB,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMtC,OAAO,GAAG,MAAMwC,MAAM,CAACC,WAAW,CAACG,UAAU,CAAC,CAAC;IACrD3C,UAAU,CAACD,OAAO,CAAC;IACnB,IAAIA,OAAO,CAAC6C,MAAM,GAAG,CAAC,IAAI,CAAC3C,cAAc,EAAE;MACzC;MACA,MAAM4C,MAAM,GAAG9C,OAAO,CAAC+C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,CAAC,CAACC,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;MACxF/C,iBAAiB,CAAC2C,MAAM,IAAI9C,OAAO,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMmD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACjD,cAAc,EAAE;MACnBO,SAAS,CAAC,4CAA4C,CAAC;MACvD;IACF;IAEA,IAAI;MACF;MACA,IAAIe,SAAS,GAAG,CAAC,EAAE;QACjB,MAAM4B,cAAc,CAAC,CAAC;MACxB;MAEA3C,SAAS,CAAC,uBAAuB,CAAC;MAClCE,oBAAoB,CAAC,CAAC,CAAC;;MAEvB;MACAyB,mBAAmB,CAACiB,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC9C3C,oBAAoB,CAAC4C,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG3C,iBAAiB,CAACK,UAAU,CAACuC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;;MAE3E;MACA,MAAMC,iBAAiB,GAAG;QACxBC,KAAK,EAAEhD,iBAAiB,CAACE,kBAAkB,GAAG;UAC5C+C,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAE/D,cAAc,CAACgE;UACtC;QACF,CAAC,GAAG,KAAK;QACTC,KAAK,EAAE;UACLJ,SAAS,EAAE;YACTC,iBAAiB,EAAE,SAAS;YAC5BC,mBAAmB,EAAE/D,cAAc,CAACgE,EAAE;YACtCE,QAAQ,EAAEZ,KAAK;YACfa,QAAQ,EAAEb,KAAK;YACfc,SAAS,EAAEb,MAAM;YACjBc,SAAS,EAAEd,MAAM;YACjBe,YAAY,EAAE1D,iBAAiB,CAACM,SAAS;YACzCqD,YAAY,EAAE3D,iBAAiB,CAACM;UAClC;QACF;MACF,CAAC;MAED,MAAMsD,YAAY,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAChB,iBAAiB,CAAC;;MAEjF;MACA,IAAIiB,gBAAgB,GAAG,IAAI;MAC3B,IAAIhE,iBAAiB,CAACG,iBAAiB,EAAE;QACvC,IAAI;UACF6D,gBAAgB,GAAG,MAAMH,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;YAC3Df,KAAK,EAAE;cACLiB,gBAAgB,EAAE,IAAI;cACtBC,gBAAgB,EAAE,IAAI;cACtBC,eAAe,EAAE,IAAI;cACrBC,UAAU,EAAE;YACd,CAAC;YACDf,KAAK,EAAE;UACT,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOgB,UAAU,EAAE;UACnBC,OAAO,CAACC,IAAI,CAAC,wDAAwD,CAAC;QACxE;MACF;;MAEA;MACA,IAAIC,iBAAiB,GAAG,IAAI;MAC5B,IAAIxE,iBAAiB,CAACI,aAAa,EAAE;QACnC,IAAI;UACFoE,iBAAiB,GAAG,MAAMX,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;YAC5DV,KAAK,EAAE;cACLX,KAAK,EAAE;gBAAE+B,KAAK,EAAE;cAAI,CAAC;cACrB9B,MAAM,EAAE;gBAAE8B,KAAK,EAAE;cAAI,CAAC;cACtBnE,SAAS,EAAE;gBAAEmE,KAAK,EAAE;cAAG;YACzB,CAAC;YACDzB,KAAK,EAAE;UACT,CAAC,CAAC;UACFvC,eAAe,CAAC+D,iBAAiB,CAAC;;UAElC;UACA,IAAIrD,SAAS,CAACoB,OAAO,EAAE;YACrBpB,SAAS,CAACoB,OAAO,CAACmC,SAAS,GAAGF,iBAAiB;UACjD;QACF,CAAC,CAAC,OAAOG,WAAW,EAAE;UACpBL,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;QAChE;MACF;;MAEA;MACA,IAAIK,cAAc,GAAGhB,YAAY;;MAEjC;MACA,IAAII,gBAAgB,EAAE;QACpB,MAAMa,WAAW,GAAGb,gBAAgB,CAACc,cAAc,CAAC,CAAC;QACrDD,WAAW,CAACE,OAAO,CAACC,KAAK,IAAIJ,cAAc,CAACK,QAAQ,CAACD,KAAK,CAAC,CAAC;MAC9D;;MAEA;MACA;;MAEA3D,SAAS,CAACkB,OAAO,GAAGqC,cAAc;;MAElC;MACA,MAAMM,QAAQ,GAAGC,kBAAkB,CAAC,CAAC;MACrC,MAAMC,aAAa,GAAG,IAAIC,aAAa,CAACT,cAAc,EAAE;QACtDM,QAAQ;QACRI,kBAAkB,EAAEC,eAAe,CAACvF,iBAAiB,CAACO,OAAO,EAAEmC,KAAK,EAAEC,MAAM;MAC9E,CAAC,CAAC;MAEF,MAAM6C,MAAM,GAAG,EAAE;MAEjBJ,aAAa,CAACK,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBJ,MAAM,CAACK,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzB;MACF,CAAC;MAEDP,aAAa,CAACU,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACR,MAAM,EAAE;UAAES,IAAI,EAAEf;QAAS,CAAC,CAAC;QACjD3F,eAAe,CAACwG,IAAI,CAAC;QACrBpG,SAAS,CAAC,2CAA2C,CAAC;;QAEtD;QACA,IAAI6E,iBAAiB,EAAE;UACrBA,iBAAiB,CAAC0B,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;UAC5D1F,eAAe,CAAC,IAAI,CAAC;QACvB;;QAEA;QACA2F,UAAU,CAAC,MAAM;UACf,IAAIlF,QAAQ,CAACqB,OAAO,IAAIwD,IAAI,EAAE;YAC5B,MAAMM,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;YAC1C7E,QAAQ,CAACqB,OAAO,CAACiE,GAAG,GAAGH,QAAQ;YAC/BnF,QAAQ,CAACqB,OAAO,CAACkE,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3B;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MAEDrF,gBAAgB,CAACmB,OAAO,GAAG6C,aAAa;MACxCA,aAAa,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE1BzH,cAAc,CAAC,IAAI,CAAC;MACpBU,SAAS,CAAC,uBAAuB,CAAC;IAEpC,CAAC,CAAC,OAAOgH,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDhH,SAAS,CAAC,sDAAsD,CAAC;MACjE,IAAI2B,mBAAmB,CAACiB,OAAO,EAAE;QAC/BqE,aAAa,CAACtF,mBAAmB,CAACiB,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMsE,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzF,gBAAgB,CAACmB,OAAO,IAAIvD,WAAW,EAAE;MAC3CoC,gBAAgB,CAACmB,OAAO,CAAC4D,IAAI,CAAC,CAAC;;MAE/B;MACA,IAAI7E,mBAAmB,CAACiB,OAAO,EAAE;QAC/BqE,aAAa,CAACtF,mBAAmB,CAACiB,OAAO,CAAC;MAC5C;;MAEA;MACA,IAAIlB,SAAS,CAACkB,OAAO,EAAE;QACrBlB,SAAS,CAACkB,OAAO,CAAC2D,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;MAC9D;;MAEA;MACA,IAAI3F,YAAY,EAAE;QAChBA,YAAY,CAAC0F,SAAS,CAAC,CAAC,CAACnB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;QACvD1F,eAAe,CAAC,IAAI,CAAC;MACvB;MAEAxB,cAAc,CAAC,KAAK,CAAC;MACrBU,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC;EAED,MAAMmH,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAM/E,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIgF,OAAO,CAAEC,OAAO,IAAK;MAC9B,IAAIC,KAAK,GAAG9G,SAAS;MACrBf,SAAS,CAAC,uBAAuB6H,KAAK,KAAK,CAAC;MAE5C,MAAMC,iBAAiB,GAAGjF,WAAW,CAAC,MAAM;QAC1CgF,KAAK,EAAE;QACP,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb7H,SAAS,CAAC,uBAAuB6H,KAAK,KAAK,CAAC;QAC9C,CAAC,MAAM;UACL7H,SAAS,CAAC,uBAAuB,CAAC;UAClCiH,aAAa,CAACa,iBAAiB,CAAC;UAChCF,OAAO,CAAC,CAAC;QACX;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMpC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMuC,KAAK,GAAG,CACZ,4BAA4B,EAC5B,4BAA4B,EAC5B,6BAA6B,EAC7B,YAAY,CACb;IAED,KAAK,MAAMzB,IAAI,IAAIyB,KAAK,EAAE;MACxB,IAAIrC,aAAa,CAACsC,eAAe,CAAC1B,IAAI,CAAC,EAAE;QACvC,OAAOA,IAAI;MACb;IACF;IACA,OAAO,YAAY;EACrB,CAAC;EAED,MAAMV,eAAe,GAAGA,CAAChF,OAAO,EAAEmC,KAAK,EAAEC,MAAM,KAAK;IAClD,MAAMiF,UAAU,GAAGlF,KAAK,GAAGC,MAAM;IACjC,MAAMkF,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC,CAAC;;IAEpC,QAAQrH,OAAO;MACb,KAAK,OAAO;QACV,OAAO0G,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI;MAAE;MAC5C,KAAK,UAAU;QACb,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;MAAE;MAC1C,KAAK,MAAM;QACT,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;MAAE;MAC1C;QACE,OAAOZ,IAAI,CAACC,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;IAC1C;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,GAAG,MAAM,KAAK;IACnD,IAAIzI,YAAY,EAAE;MAChB,IAAI;QACF,IAAIyI,MAAM,KAAK,MAAM,EAAE;UACrB;UACA,MAAMC,GAAG,GAAG1B,GAAG,CAACC,eAAe,CAACjH,YAAY,CAAC;UAC7C,MAAM2I,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGJ,GAAG;UACZC,CAAC,CAACI,QAAQ,GAAG,aAAa,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UACzFR,CAAC,CAACS,KAAK,CAAC,CAAC;UACTpC,GAAG,CAACqC,eAAe,CAACX,GAAG,CAAC;UACxBjI,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,MAAM,IAAIgI,MAAM,KAAK,KAAK,EAAE;UAC3BpI,SAAS,CAAC,sBAAsB,CAAC;;UAEjC;UACA,MAAMiJ,WAAW,GAAG,MAAMtJ,YAAY,CAACsJ,WAAW,CAAC,CAAC;UACpD,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACF,WAAW,CAAC;;UAE9C;UACA,MAAMG,QAAQ,GAAG,aAAa,IAAIT,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;UAC7F,MAAMO,SAAS,GAAG,MAAMtH,MAAM,CAACC,WAAW,CAACsH,aAAa,CAACJ,UAAU,EAAEE,QAAQ,CAAC;UAE9E,IAAIC,SAAS,EAAE;YACbrJ,SAAS,CAAC,yBAAyB,CAAC;YACpCI,oBAAoB,CAAC,KAAK,CAAC;UAC7B;QACF;MACF,CAAC,CAAC,OAAO4G,KAAK,EAAE;QACdrC,OAAO,CAACqC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDhH,SAAS,CAAC,0BAA0B,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMuJ,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAIhI,QAAQ,CAACqB,OAAO,IAAIrB,QAAQ,CAACqB,OAAO,CAACiE,GAAG,EAAE;MAC5CF,GAAG,CAACqC,eAAe,CAACzH,QAAQ,CAACqB,OAAO,CAACiE,GAAG,CAAC;MACzCtF,QAAQ,CAACqB,OAAO,CAACiE,GAAG,GAAG,EAAE;IAC3B;IACAjH,eAAe,CAAC,IAAI,CAAC;IACrBI,SAAS,CAAC,iBAAiB,CAAC;EAC9B,CAAC;EAED,oBACEhB,OAAA;IAAKwK,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBzK,OAAA;MAAQwK,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACxBzK,OAAA;QAAAyK,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB7K,OAAA;QAAKwK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzK,OAAA;UACEwK,SAAS,EAAC,mBAAmB;UAC7BM,OAAO,EAAEA,CAAA,KAAM5I,eAAe,CAAC,CAACD,YAAY,CAAE;UAAAwI,QAAA,EAC/C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRhK,WAAW,CAACwC,MAAM,KAAK,QAAQ,iBAC9BrD,OAAA;UAAQwK,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAE7H,kBAAmB;UAAAwH,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAER5I,YAAY,iBACXjC,OAAA;MAAKwK,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzK,OAAA;QAAAyK,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE3B7K,OAAA;QAAKwK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzK,OAAA;UAAAyK,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd7K,OAAA;UAAOwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BzK,OAAA;YACEsH,IAAI,EAAC,UAAU;YACfyD,OAAO,EAAE1J,iBAAiB,CAACG,iBAAkB;YAC7CwJ,QAAQ,EAAGC,CAAC,IAAK3J,oBAAoB,CAACwC,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPtC,iBAAiB,EAAEyJ,CAAC,CAACC,MAAM,CAACH;YAC9B,CAAC,CAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,sBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7K,OAAA;UAAOwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BzK,OAAA;YACEsH,IAAI,EAAC,UAAU;YACfyD,OAAO,EAAE1J,iBAAiB,CAACE,kBAAmB;YAC9CyJ,QAAQ,EAAGC,CAAC,IAAK3J,oBAAoB,CAACwC,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPvC,kBAAkB,EAAE0J,CAAC,CAACC,MAAM,CAACH;YAC/B,CAAC,CAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,wBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7K,OAAA;QAAKwK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzK,OAAA;UAAAyK,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd7K,OAAA;UAAOwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BzK,OAAA;YACEsH,IAAI,EAAC,UAAU;YACfyD,OAAO,EAAE1J,iBAAiB,CAACI,aAAc;YACzCuJ,QAAQ,EAAGC,CAAC,IAAK3J,oBAAoB,CAACwC,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPrC,aAAa,EAAEwJ,CAAC,CAACC,MAAM,CAACH;YAC1B,CAAC,CAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,kBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAER7K,OAAA;UAAKwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzK,OAAA;YAAAyK,QAAA,EAAO;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1B7K,OAAA;YACEmL,KAAK,EAAE9J,iBAAiB,CAACK,UAAW;YACpCsJ,QAAQ,EAAGC,CAAC,IAAK3J,oBAAoB,CAACwC,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPpC,UAAU,EAAEuJ,CAAC,CAACC,MAAM,CAACC;YACvB,CAAC,CAAC,CAAE;YAAAV,QAAA,gBAEJzK,OAAA;cAAQmL,KAAK,EAAC,UAAU;cAAAV,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjD7K,OAAA;cAAQmL,KAAK,EAAC,WAAW;cAAAV,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpD7K,OAAA;cAAQmL,KAAK,EAAC,WAAW;cAAAV,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpD7K,OAAA;cAAQmL,KAAK,EAAC,WAAW;cAAAV,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAKwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzK,OAAA;YAAAyK,QAAA,EAAO;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1B7K,OAAA;YACEmL,KAAK,EAAE9J,iBAAiB,CAACM,SAAU;YACnCqJ,QAAQ,EAAGC,CAAC,IAAK3J,oBAAoB,CAACwC,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPnC,SAAS,EAAEyJ,QAAQ,CAACH,CAAC,CAACC,MAAM,CAACC,KAAK;YACpC,CAAC,CAAC,CAAE;YAAAV,QAAA,gBAEJzK,OAAA;cAAQmL,KAAK,EAAE,EAAG;cAAAV,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC7K,OAAA;cAAQmL,KAAK,EAAE,EAAG;cAAAV,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAKwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzK,OAAA;YAAAyK,QAAA,EAAO;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvB7K,OAAA;YACEmL,KAAK,EAAE9J,iBAAiB,CAACO,OAAQ;YACjCoJ,QAAQ,EAAGC,CAAC,IAAK3J,oBAAoB,CAACwC,IAAI,KAAK;cAC7C,GAAGA,IAAI;cACPlC,OAAO,EAAEqJ,CAAC,CAACC,MAAM,CAACC;YACpB,CAAC,CAAC,CAAE;YAAAV,QAAA,gBAEJzK,OAAA;cAAQmL,KAAK,EAAC,OAAO;cAAAV,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnD7K,OAAA;cAAQmL,KAAK,EAAC,UAAU;cAAAV,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C7K,OAAA;cAAQmL,KAAK,EAAC,MAAM;cAAAV,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7K,OAAA;QAAKwK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzK,OAAA;UAAAyK,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClB7K,OAAA;UAAKwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzK,OAAA;YAAAyK,QAAA,EAAO;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzC7K,OAAA;YACEmL,KAAK,EAAEpJ,SAAU;YACjBiJ,QAAQ,EAAGC,CAAC,IAAKjJ,YAAY,CAACoJ,QAAQ,CAACH,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAE;YAAAV,QAAA,gBAExDzK,OAAA;cAAQmL,KAAK,EAAE,CAAE;cAAAV,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC7K,OAAA;cAAQmL,KAAK,EAAE,CAAE;cAAAV,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC7K,OAAA;cAAQmL,KAAK,EAAE,CAAE;cAAAV,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC7K,OAAA;cAAQmL,KAAK,EAAE,EAAG;cAAAV,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED7K,OAAA;MAAMwK,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACvBzK,OAAA;QAAKwK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzK,OAAA;UAAAyK,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjC7K,OAAA;UAAKwK,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BlK,OAAO,CAAC2D,GAAG,CAAEmH,MAAM,iBAClBrL,OAAA;YAEEwK,SAAS,EAAE,eAAe,CAAA/J,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgE,EAAE,MAAK4G,MAAM,CAAC5G,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/EqG,OAAO,EAAEA,CAAA,KAAMpK,iBAAiB,CAAC2K,MAAM,CAAE;YAAAZ,QAAA,gBAEzCzK,OAAA;cACE6H,GAAG,EAAEwD,MAAM,CAACC,SAAU;cACtBC,GAAG,EAAEF,MAAM,CAAC7H,IAAK;cACjBgH,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACF7K,OAAA;cAAKwK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEY,MAAM,CAAC7H;YAAI;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAT3CQ,MAAM,CAAC5G,EAAE;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7K,OAAA;QAAKwK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5B9J,YAAY,gBACXX,OAAA;UAAKwK,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzK,OAAA;YACEwL,GAAG,EAAEjJ,QAAS;YACdiI,SAAS,EAAC,eAAe;YACzBiB,QAAQ;YACRC,QAAQ,EAAE,KAAM;YAChBC,OAAO,EAAC,UAAU;YAClBC,KAAK;YACLC,WAAW;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACF7K,OAAA;YAAKwK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BzK,OAAA;cAAAyK,QAAA,GAAG,uCAAgC,EAACtC,cAAc,CAAClH,iBAAiB,CAAC;YAAA;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN7K,OAAA;UAAKwK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BzK,OAAA;YAAKwK,SAAS,EAAE,cAAcnK,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;YAAAoK,QAAA,EAC5DpK,WAAW,gBACVL,OAAA;cAAKwK,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BzK,OAAA;gBAAKwK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCzK,OAAA;kBAAMwK,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,aAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN7K,OAAA;gBAAKwK,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAChCtC,cAAc,CAAClH,iBAAiB;cAAC;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACN7K,OAAA;gBAAKwK,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BhK,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE+C;cAAI;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACN7K,OAAA;gBAAKwK,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GACrCpJ,iBAAiB,CAACK,UAAU,EAAC,UAAG,EAACL,iBAAiB,CAACM,SAAS,EAAC,aAAM,EAACN,iBAAiB,CAACO,OAAO;cAAA;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN7K,OAAA;cAAKwK,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BzK,OAAA;gBAAKwK,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE1J;cAAM;gBAAA2J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1CpK,cAAc,iBACbT,OAAA;gBAAKwK,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,mBAAiB,EAAChK,cAAc,CAAC+C,IAAI;cAAA;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLhJ,YAAY,IAAIxB,WAAW,iBAC1BL,OAAA;YAAKwK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzK,OAAA;cACEwL,GAAG,EAAEhJ,SAAU;cACfgI,SAAS,EAAC,cAAc;cACxBkB,QAAQ;cACRE,KAAK;cACLC,WAAW;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACF7K,OAAA;cAAKwK,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN7K,OAAA;QAAKwK,SAAS,EAAC,UAAU;QAAAC,QAAA,GACtB,CAACpK,WAAW,IAAI,CAACM,YAAY,iBAC5BX,OAAA;UACEwK,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEpH,cAAe;UACxBoI,QAAQ,EAAE,CAACrL,cAAc,IAAII,WAAW,CAACwC,MAAM,KAAK,QAAS;UAAAoH,QAAA,EAC9D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAxK,WAAW,iBACVL,OAAA;UAAQwK,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAE5C,aAAc;UAAAuC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAlK,YAAY,IAAI,CAACN,WAAW,iBAC3BL,OAAA,CAAAE,SAAA;UAAAuK,QAAA,GACG,CAACtJ,iBAAiB,gBACjBnB,OAAA;YACEwK,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAM1J,oBAAoB,CAAC,IAAI,CAAE;YAAAqJ,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET7K,OAAA;YAAKwK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzK,OAAA;cAAAyK,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvB7K,OAAA;cACEwK,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC,MAAM,CAAE;cAAAsB,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7K,OAAA;cACEwK,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC,KAAK,CAAE;cAAAsB,QAAA,EACzC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7K,OAAA;cACEwK,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAEA,CAAA,KAAM1J,oBAAoB,CAAC,KAAK,CAAE;cAAAqJ,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACD7K,OAAA;YAAQwK,SAAS,EAAC,mBAAmB;YAACM,OAAO,EAAEP,cAAe;YAAAE,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,eAED7K,OAAA;UAAQwK,SAAS,EAAC,mBAAmB;UAACM,OAAO,EAAEjI,WAAY;UAAA4H,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET7K,OAAA;UACEwK,SAAS,EAAC,KAAK;UACfuB,KAAK,EAAE;YAACC,UAAU,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAO,CAAE;UAC9CnB,OAAO,EAAEA,CAAA,KAAMoB,KAAK,CAAC,iCAAiC,CAAE;UAAAzB,QAAA,EACzD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACzK,EAAA,CAzmBQD,GAAG;AAAAgM,EAAA,GAAHhM,GAAG;AA2mBZ,eAAeA,GAAG;AAAC,IAAAgM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}